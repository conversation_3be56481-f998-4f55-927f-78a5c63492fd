lines = """
good_click_softsearch_item_id_list_top50 = block_data[93].output
good_click_softsearch_seller_id_list_top50 = block_data[94].output
good_click_softsearch_lag_list_top50 = block_data[95].output
good_click_softsearch_category_list_top50 = block_data[96].output
good_click_softsearch_carry_type_list_top50 = block_data[97].output
good_click_softsearch_price_list_top50 = block_data[98].output
good_click_softsearch_item_count_list_top50 = block_data[99].output
good_click_softsearch_topk_values = block_data[100].output
good_click_softsearch_topk_indices = block_data[101].output
""".strip().splitlines()

index = 90
for line in lines:
    # 替换idx
    pass
    index += 1