from openai import OpenAI

API_KEY = "sk-hyOzsUNA4WGfCyWsiP0pO3Dxp88nkdrOXpqm6rC1ipOiqn2w"
client = OpenAI(api_key=API_KEY,
                base_url='https://api.chatanywhere.tech/v1')

deepseek_client = OpenAI(
    api_key="***********************************",
    base_url="https://api.deepseek.com",
    timeout=60.0  # 增加超时时间到60秒
)


def non_stream(prompt):
    response = client.chat.completions.create(
            model="o3",
            messages=[
                {
                  "role": "user",
                  "content": prompt
                }
              ],
            )
    full_response = response.choices[0].message.content.strip()
    print(full_response)

def stream(prompt):
    response = client.chat.completions.create(
            model="o3",
            messages=[
                {
                  "role": "user",
                  "content": prompt
                }
              ],
            stream=True,
            # stream_options={"include_usage":True}
            )
    model_reply = ''
    for chunk in response:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end=None)
                model_reply += content
if __name__ == "__main__":
    t = r"""
我需要编写一个矩阵吸收的多头target attention函数，但是限制是不能使用四维矩阵乘法，也不能使用for循环+slice，有没有别的办法能够实现？比如reshape或拼接成三维矩阵乘法，计算出矩阵乘积之后再复原成四维多头矩阵。
允许使用的tensorflow算子包括：
tf.fill
tf.placeholder
tf.const
tf.identity
tf.stop_gradient
tf.stack
tf.concat
tf.concat
tf.matmul
tf.matmul
tf.matmul
tf.nn.bias_add
tf.nn.softmax
tf.math.rsqrt
tf.split
tf.split
tf.slice
tf.reshape
tf.expand_dims
tf.transpose
tf.math.reduce_mean
tf.math.reduce_sum
tf.math.reduce_prod
tf.math.reduce_max
tf.math.reduce_min
tf.math.square
tf.math.squared_difference
tf.gather
tf.gather
tf.no_op

原单头Attention函数为：
def transformer_component_v2_absorb(self,query_input, action_list_input, col, name, nh=1, att_emb_size=32):
        assert nh == 1
        action_item_size = action_list_input.get_shape().as_list()[-1]
        Q = tf.get_variable(name + 'q_trans_matrix', (col, att_emb_size * nh))  # [emb, att_emb * hn]
        K = tf.get_variable(name + 'k_trans_matrix', (action_item_size, att_emb_size * nh))
        V = tf.get_variable(name + 'v_trans_matrix', (action_item_size, att_emb_size * nh))
        querys = tf.matmul(query_input, Q)  # (batch_size,sq_q,att_embedding_size*head_num)
        qk = tf.matmul(querys,K,transpose_b=True)/ 8.0 #[bs,sq_q,k_dim]
        inner_product = tf.matmul(qk,action_list_input,transpose_b=True) #[bs,sq_q,seq_key]
        normalized_att_scores = tf.nn.softmax(inner_product)
        result = tf.matmul(normalized_att_scores, action_list_input)  #[bs,seq_q,value_dim]
        result = tf.matmul(result,V)
        mha_result = tf.reshape(result, [-1, nh * att_emb_size])
        return mha_result
"""
    non_stream(t)