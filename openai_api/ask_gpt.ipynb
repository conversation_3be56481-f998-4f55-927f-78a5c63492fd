{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "API_KEY = \"sk-hyOzsUNA4WGfCyWsiP0pO3Dxp88nkdrOXpqm6rC1ipOiqn2w\"\n", "client = OpenAI(api_key=API_KEY,\n", "                base_url='https://api.chatanywhere.tech/v1')\n", "\n", "deepseek_client = OpenAI(\n", "    api_key=\"***********************************\",\n", "    base_url=\"https://api.deepseek.com\",\n", "    timeout=60.0  # 增加超时时间到60秒\n", ")\n", "\n", "\n", "def non_stream(prompt):\n", "    response = client.chat.completions.create(\n", "            model=\"o3\",\n", "            messages=[\n", "                {\n", "                  \"role\": \"user\",\n", "                  \"content\": prompt\n", "                }\n", "              ],\n", "            )\n", "    full_response = response.choices[0].message.content.strip()\n", "    print(full_response)\n", "\n", "def stream(prompt):\n", "    response = client.chat.completions.create(\n", "            model=\"o3\",\n", "            messages=[\n", "                {\n", "                  \"role\": \"user\",\n", "                  \"content\": prompt\n", "                }\n", "              ],\n", "            stream=True,\n", "            # stream_options={\"include_usage\":True}\n", "            )\n", "    model_reply = ''\n", "    for chunk in response:\n", "            if chunk.choices[0].delta.content is not None:\n", "                content = chunk.choices[0].delta.content\n", "                print(content, end=None)\n", "                model_reply += content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'non_stream' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 51\u001b[0m\n\u001b[1;32m      1\u001b[0m t \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;124m我需要编写一个矩阵吸收的多头target attention函数，但是限制是不能使用四维矩阵乘法，也不能使用for循环+slice，有没有别的办法能够实现？比如reshape或拼接成三维矩阵乘法，计算出矩阵乘积之后再复原成四维多头矩阵。\u001b[39m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;124m允许使用的tensorflow算子包括：\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     49\u001b[0m \u001b[38;5;124m        return mha_result\u001b[39m\n\u001b[1;32m     50\u001b[0m \u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m---> 51\u001b[0m non_stream(t)\n", "\u001b[0;31mNameError\u001b[0m: name 'non_stream' is not defined"]}], "source": ["\n", "t = r\"\"\"\n", "我需要编写一个矩阵吸收的多头target attention函数。矩阵吸收的意思是，query * Qw之后，必须要先乘Qk，再乘action_list_input得到内积，不能分开算query * Qw 和 action_list_input * Qk。\n", "但是限制是不能使用四维矩阵乘法，也不能使用for循环+slice，有没有别的办法能够实现？比如reshape或拼接成三维矩阵乘法，计算出矩阵乘积之后再复原成四维多头矩阵。\n", "允许使用的tensorflow算子包括：\n", "tf.fill\n", "tf.placeholder\n", "tf.const\n", "tf.identity\n", "tf.stop_gradient\n", "tf.stack\n", "tf.concat\n", "tf.concat\n", "tf.mat<PERSON>l\n", "tf.mat<PERSON>l\n", "tf.mat<PERSON>l\n", "tf.nn.bias_add\n", "tf.nn.softmax\n", "tf.math.rsqrt\n", "tf.split\n", "tf.split\n", "tf.slice\n", "tf.reshape\n", "tf.expand_dims\n", "tf.transpose\n", "tf.math.reduce_mean\n", "tf.math.reduce_sum\n", "tf.math.reduce_prod\n", "tf.math.reduce_max\n", "tf.math.reduce_min\n", "tf.math.square\n", "tf.math.squared_difference\n", "tf.gather\n", "tf.gather\n", "tf.no_op\n", "\n", "原单头Attention函数为：\n", "def transformer_component_v2_absorb(self,query_input, action_list_input, col, name, nh=1, att_emb_size=32):\n", "        assert nh == 1\n", "        action_item_size = action_list_input.get_shape().as_list()[-1]\n", "        Q = tf.get_variable(name + 'q_trans_matrix', (col, att_emb_size * nh))  # [emb, att_emb * hn]\n", "        K = tf.get_variable(name + 'k_trans_matrix', (action_item_size, att_emb_size * nh))\n", "        V = tf.get_variable(name + 'v_trans_matrix', (action_item_size, att_emb_size * nh))\n", "        querys = tf.matmul(query_input, Q)  # (batch_size,sq_q,att_embedding_size*head_num)\n", "        qk = tf.matmul(querys,K,transpose_b=True)/ 8.0 #[bs,sq_q,k_dim]\n", "        inner_product = tf.matmul(qk,action_list_input,transpose_b=True) #[bs,sq_q,seq_key]\n", "        normalized_att_scores = tf.nn.softmax(inner_product)\n", "        result = tf.matmul(normalized_att_scores, action_list_input)  #[bs,seq_q,value_dim]\n", "        result = tf.matmul(result,V)\n", "        mha_result = tf.reshape(result, [-1, nh * att_emb_size])\n", "        return mha_result\n", "\"\"\"\n", "non_stream(t)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "t = r\"\"\"\n", "是否存在一个方法，能够将h个[b, s, d]矩阵和[d, x]矩阵\n", "允许使用的tensorflow算子包括：\n", "tf.fill\n", "tf.placeholder\n", "tf.const\n", "tf.identity\n", "tf.stop_gradient\n", "tf.stack\n", "tf.concat\n", "tf.concat\n", "tf.mat<PERSON>l\n", "tf.mat<PERSON>l\n", "tf.mat<PERSON>l\n", "tf.nn.bias_add\n", "tf.nn.softmax\n", "tf.math.rsqrt\n", "tf.split\n", "tf.split\n", "tf.slice\n", "tf.reshape\n", "tf.expand_dims\n", "tf.transpose\n", "tf.math.reduce_mean\n", "tf.math.reduce_sum\n", "tf.math.reduce_prod\n", "tf.math.reduce_max\n", "tf.math.reduce_min\n", "tf.math.square\n", "tf.math.squared_difference\n", "tf.gather\n", "tf.gather\n", "tf.no_op\n", "\n", "原单头Attention函数为：\n", "def transformer_component_v2_absorb(self,query_input, action_list_input, col, name, nh=1, att_emb_size=32):\n", "        assert nh == 1\n", "        action_item_size = action_list_input.get_shape().as_list()[-1]\n", "        Q = tf.get_variable(name + 'q_trans_matrix', (col, att_emb_size * nh))  # [emb, att_emb * hn]\n", "        K = tf.get_variable(name + 'k_trans_matrix', (action_item_size, att_emb_size * nh))\n", "        V = tf.get_variable(name + 'v_trans_matrix', (action_item_size, att_emb_size * nh))\n", "        querys = tf.matmul(query_input, Q)  # (batch_size,sq_q,att_embedding_size*head_num)\n", "        qk = tf.matmul(querys,K,transpose_b=True)/ 8.0 #[bs,sq_q,k_dim]\n", "        inner_product = tf.matmul(qk,action_list_input,transpose_b=True) #[bs,sq_q,seq_key]\n", "        normalized_att_scores = tf.nn.softmax(inner_product)\n", "        result = tf.matmul(normalized_att_scores, action_list_input)  #[bs,seq_q,value_dim]\n", "        result = tf.matmul(result,V)\n", "        mha_result = tf.reshape(result, [-1, nh * att_emb_size])\n", "        return mha_result\n", "\"\"\"\n", "non_stream(t)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}