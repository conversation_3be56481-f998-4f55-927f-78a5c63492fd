{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["colossus_rs_category_list=\"category_list\",\n", "colossus_rs_exposure_ratio_list=\"exposure_ratio_list\",\n", "colossus_rs_item_id_list=\"commodity_id_list\",\n", "colossus_rs_pagecode_id_list=\"pagecode_id_list\",\n", "colossus_rs_seller_id_list=\"seller_id_list\",\n", "colossus_rs_timestamp_list=\"timestamp_list\",\n", "colossus_rs_uniform_spu_id_list=\"uniform_spu_id_list\",\n"]}], "source": ["d = {        \"origin_colossus_rs_category_list\":\"good_show_category_list\",\n", "              \"origin_colossus_rs_exposure_ratio_list\":\"good_show_exposure_ratio_list\",\n", "              \"origin_colossus_rs_item_id_list\":\"good_show_commodity_id_list\",\n", "              \"origin_colossus_rs_pagecode_id_list\":\"good_show_pagecode_id_list\",\n", "              \"origin_colossus_rs_seller_id_list\":\"good_show_seller_id_list\",\n", "              \"origin_colossus_rs_timestamp_list\":\"good_show_timestamp_list\",\n", "              \"origin_colossus_rs_uniform_spu_id_list\":\"good_show_uniform_spu_id_list\",\n", "}\n", "for k,v in d.items():\n", "    k = k.replace('origin_','')\n", "    v = v.replace('good_show_','')\n", "    if isinstance(v, str):\n", "        v = f'\"{v}\"'\n", "    print(f'{k}={v},')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"colossus_rs_category_list\": \"category_list\",\n", "\"colossus_rs_exposure_ratio_list\": \"exposure_ratio_list\",\n", "\"colossus_rs_item_id_list\": \"commodity_id_list\",\n", "\"colossus_rs_pagecode_id_list\": \"pagecode_id_list\",\n", "\"colossus_rs_seller_id_list\": \"seller_id_list\",\n", "\"colossus_rs_timestamp_list\": \"timestamp_list\",\n", "\"colossus_rs_uniform_spu_id_list\": \"uniform_spu_id_list\",\n", "\"cate1_attr\": \"photo_goods_iCate1Id\",\n", "\"cate2_attr\": \"photo_goods_iCate2Id\",\n", "\"cate3_attr\": \"photo_goods_iCate3Id\",\n", "\"gsu_limit_num_attr\": 10,\n", "\"gsu_prefix_attr\": \"gsu_\",\n", "\"input_prefix_attr\": \"good_show_\",\n", "\"limit_num_attr\": 100,\n", "\"seconds_to_lookback_attr\": 120\n"]}], "source": ["a = \"\"\"colossus_rs_category_list=\"category_list\",\n", "colossus_rs_exposure_ratio_list=\"exposure_ratio_list\",\n", "colossus_rs_item_id_list=\"commodity_id_list\",\n", "colossus_rs_pagecode_id_list=\"pagecode_id_list\",\n", "colossus_rs_seller_id_list=\"seller_id_list\",\n", "colossus_rs_timestamp_list=\"timestamp_list\",\n", "colossus_rs_uniform_spu_id_list=\"uniform_spu_id_list\",\n", "cate1_attr=\"photo_goods_iCate1Id\",\n", "cate2_attr=\"photo_goods_iCate2Id\",\n", "cate3_attr=\"photo_goods_iCate3Id\",\n", "gsu_limit_num_attr=10,\n", "gsu_prefix_attr=\"gsu_\",\n", "input_prefix_attr=\"good_show_\",\n", "limit_num_attr=100,\n", "seconds_to_lookback_attr=120\"\"\".splitlines()\n", "for i in a:\n", "    x,y = i.split('=')\n", "    print(f'\"{x}\": {y}')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}