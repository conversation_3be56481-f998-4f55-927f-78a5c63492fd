import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Read the CSV file
df = pd.read_csv('2025062617热门top1.csv')

# Extract and convert the values from string format [value] to float
def extract_value(val_str):
    try:
        # Remove brackets and convert to float
        return float(val_str.strip('[]'))
    except:
        return np.nan

# Process each column
df['cvr'] = df['a.extractcombinedenseinnerordercvr'].apply(extract_value)
df['origin_price'] = df['a.extractcombinedenseoriginpricenoprefix'].apply(extract_value)
df['auction_bid'] = df['a.extractcombinedensefrontauctionbidnoprefix'].apply(extract_value)

# Create a figure with 2 subplots
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Scatter plot: CVR vs Origin Price
axes[0].scatter(df['cvr'], df['origin_price'], alpha=0.5, s=10)
axes[0].set_title('CVR vs Origin Price')
axes[0].set_xlabel('CVR Value')
axes[0].set_ylabel('Origin Price')
axes[0].set_xlim(0, 0.01)
axes[0].set_ylim(0, 250)
axes[0].grid(True, linestyle='--', alpha=0.7)

# Scatter plot: CVR vs Auction Bid
axes[1].scatter(df['cvr'], df['auction_bid'], alpha=0.5, s=10)
axes[1].set_title('CVR vs Auction Bid')
axes[1].set_xlabel('CVR Value')
axes[1].set_ylabel('Auction Bid')
axes[1].set_xlim(0, 0.01)
axes[1].set_ylim(0, 300)
axes[1].grid(True, linestyle='--', alpha=0.7)

plt.tight_layout()
plt.savefig('cvr_relationships.png')
plt.show()