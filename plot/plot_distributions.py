import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Read the CSV file
df = pd.read_csv('2025062617热门top1.csv')

# Extract and convert the values from string format [value] to float
def extract_value(val_str):
    try:
        # Remove brackets and convert to float
        return float(val_str.strip('[]'))
    except:
        return np.nan

# Process each column
df['cvr'] = df['a.extractcombinedenseinnerordercvr'].apply(extract_value)
df['origin_price'] = df['a.extractcombinedenseoriginpricenoprefix'].apply(extract_value)
df['auction_bid'] = df['a.extractcombinedensefrontauctionbidnoprefix'].apply(extract_value)

# Create a figure with 3 subplots
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

# Plot histograms for each column with adjusted x-axis ranges
axes[0].hist(df['cvr'].dropna(), bins=200, alpha=0.7, range=(0, 0.01))
axes[0].set_title('CVR Distribution')
axes[0].set_xlabel('CVR Value')
axes[0].set_ylabel('Frequency')
axes[0].set_xlim(0, 0.01)

axes[1].hist(df['origin_price'].dropna(), bins=200, alpha=0.7, range=(0, 250))
axes[1].set_title('Second Price Distribution')
axes[1].set_xlabel('Price Value')
axes[1].set_ylabel('Frequency')
axes[1].set_xlim(0, 250)

axes[2].hist(df['auction_bid'].dropna(), bins=200, alpha=0.7, range=(0, 300))
axes[2].set_title('Auction Bid Distribution')
axes[2].set_xlabel('Bid Value')
axes[2].set_ylabel('Frequency')
axes[2].set_xlim(0, 300)

plt.tight_layout()
plt.savefig('distributions-v2.png')
plt.show()
