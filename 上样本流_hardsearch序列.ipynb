{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["填哈希前的特征名。和对应的slot起始点，以及需要的size  创建对应map"]}, {"cell_type": "code", "execution_count": 233, "metadata": {}, "outputs": [], "source": ["add_feature = [\"gsu_colossus_rs_count_index_list\",\n", "              \"gsu_colossus_rs_exposure_ratio_list\",\n", "              \"gsu_colossus_rs_item_id_list\",\n", "              \"gsu_colossus_rs_lagV1_list\",\n", "              \"gsu_colossus_rs_lagV2_list\",\n", "              \"gsu_colossus_rs_pagecode_id_list\",\n", "              \"gsu_colossus_rs_uniform_spu_id_list\"]\n", "\n", "first_slot = 1007\n", "# remap_slot=1014, dim=4, map_slot=1014, size=0, slot=0, name=colossus_rs_count_index_list, category=ad, field=308, feature_size=101\n", "# remap_slot=1017, dim=16, map_slot=1017, size=0, slot=0, name=colossus_rs_item_id_list, category=ad, field=309, feature_size=10000001\n", "# remap_slot=1018, dim=4, map_slot=1018, size=0, slot=0, name=colossus_rs_lagV1_list, category=ad, field=310, feature_size=101\n", "# remap_slot=1019, dim=4, map_slot=1019, size=0, slot=0, name=colossus_rs_lagV2_list, category=ad, field=311, feature_size=10001\n", "# remap_slot=1020, dim=4, map_slot=1020, size=0, slot=0, name=colossus_rs_pagecode_id_list, category=ad, field=312, feature_size=101\n", "# remap_slot=1022, dim=16, map_slot=1022, size=0, slot=0, name=colossus_rs_uniform_spu_id_list, category=ad, field=313, feature_size=10000001\n", "size_map = {\n", "    \"gsu_colossus_rs_count_index_list\":101,\n", "    \"gsu_colossus_rs_exposure_ratio_list\":1001,\n", "    \"gsu_colossus_rs_item_id_list\":10000001,\n", "    \"gsu_colossus_rs_lagV1_list\":101,\n", "    \"gsu_colossus_rs_lagV2_list\":10001,\n", "    \"gsu_colossus_rs_pagecode_id_list\":101,\n", "    \"gsu_colossus_rs_uniform_spu_id_list\":10000001,\n", "}\n", "prefix = \"gsu_colossus_rs_\"\n", "kuiba_prefix = \"colossus_rs_hardsearch_\"\n", "final_prefix = \"good_show_hardsearch_\""]}, {"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [], "source": ["name2slot_map = {name: first_slot + i for i, name in enumerate(add_feature)}\n", "slot2name_map = {v: k for k, v in name2slot_map.items()}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建kuiba config"]}, {"cell_type": "code", "execution_count": 235, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'gsu_colossus_rs_count_index_list': {'attrs': [{'attr': ['gsu_colossus_rs_count_index_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1007}]},\n", " 'gsu_colossus_rs_exposure_ratio_list': {'attrs': [{'attr': ['gsu_colossus_rs_exposure_ratio_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1008}]},\n", " 'gsu_colossus_rs_item_id_list': {'attrs': [{'attr': ['gsu_colossus_rs_item_id_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1009}]},\n", " 'gsu_colossus_rs_lagV1_list': {'attrs': [{'attr': ['gsu_colossus_rs_lagV1_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1010}]},\n", " 'gsu_colossus_rs_lagV2_list': {'attrs': [{'attr': ['gsu_colossus_rs_lagV2_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1011}]},\n", " 'gsu_colossus_rs_pagecode_id_list': {'attrs': [{'attr': ['gsu_colossus_rs_pagecode_id_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1012}]},\n", " 'gsu_colossus_rs_uniform_spu_id_list': {'attrs': [{'attr': ['gsu_colossus_rs_uniform_spu_id_list'],\n", "    'converter': 'list',\n", "    'converter_args': {'limit': 100,\n", "     'reversed': <PERSON><PERSON><PERSON>,\n", "     'enable_filter': False},\n", "    'key_type': 1013}]}}"]}, "execution_count": 235, "metadata": {}, "output_type": "execute_result"}], "source": ["kuiba_param_config = [(k,v,k) for k,v in name2slot_map.items()]\n", "config = {}\n", "for key, slot, feat in kuiba_param_config:\n", "    config[key] = {\n", "        \"attrs\": [{\n", "            \"attr\": [feat],\n", "            \"converter\": \"list\",\n", "            \"converter_args\": {\"limit\": 100, \"reversed\": False, \"enable_filter\": False},\n", "            \"key_type\": slot\n", "        }]\n", "    }\n", "config"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据featuresize, 创建item map"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["slot_as_attr_name_prefix=\"colossus_rs_hardsearch_\",\n", "item_map_list = \n"]}, {"data": {"text/plain": ["[['1007', 1007, 1007, 101],\n", " ['1008', 1008, 1008, 1001],\n", " ['1009', 1009, 1009, 10000001],\n", " ['1010', 1010, 1010, 101],\n", " ['1011', 1011, 1011, 10001],\n", " ['1012', 1012, 1012, 101],\n", " ['1013', 1013, 1013, 10000001]]"]}, "execution_count": 236, "metadata": {}, "output_type": "execute_result"}], "source": ["item_map = [[str(slot), slot, slot, size_map[name]] for name,slot in name2slot_map.items()]\n", "\n", "print(f'slot_as_attr_name_prefix=\"{kuiba_prefix}\",')\n", "print('item_map_list = ')\n", "item_map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建copy attr参数"]}, {"cell_type": "code", "execution_count": 237, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'from_item': 'colossus_rs_hardsearch_1007',\n", "  'to_item': 'good_show_hardsearch_count_index_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1008',\n", "  'to_item': 'good_show_hardsearch_exposure_ratio_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1009',\n", "  'to_item': 'good_show_hardsearch_item_id_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1010',\n", "  'to_item': 'good_show_hardsearch_lagV1_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1011',\n", "  'to_item': 'good_show_hardsearch_lagV2_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1012',\n", "  'to_item': 'good_show_hardsearch_pagecode_id_list'},\n", " {'from_item': 'colossus_rs_hardsearch_1013',\n", "  'to_item': 'good_show_hardsearch_uniform_spu_id_list'}]"]}, "execution_count": 237, "metadata": {}, "output_type": "execute_result"}], "source": ["copy_attr = [{\"from_item\": f\"{kuiba_prefix}{slot}\", \"to_item\": f\"{name.replace(prefix, final_prefix)}\"} for name,slot in name2slot_map.items()]\n", "copy_attr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# fg_schema_out"]}, {"cell_type": "code", "execution_count": 238, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["column_name=good_show_hardsearch_count_index_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_exposure_ratio_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_item_id_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_lagV1_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_lagV2_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_pagecode_id_list, compression_type=None, column_type=List<Long>\n", "column_name=good_show_hardsearch_uniform_spu_id_list, compression_type=None, column_type=List<Long>\n"]}, {"data": {"text/plain": ["7"]}, "execution_count": 238, "metadata": {}, "output_type": "execute_result"}], "source": ["final_name = [name.replace(prefix, final_prefix) for name,slot in name2slot_map.items()]\n", "\n", "\n", "for i in final_name:\n", "    print(f\"column_name={i}, compression_type=None, column_type=List<Long>\")\n", "\n", "len(final_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 样本流sql"]}, {"cell_type": "code", "execution_count": 239, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["colossus_rs_uniform_spu_id_list_share,\n", "colossus_rs_category_1_list,\n", "colossus_rs_is_click_list,\n", "colossus_rs_category_2_list,\n", "colossus_rs_item_id_list_share,\n", "colossus_rs_category_3_list,\n", "colossus_rs_seller_id_list,\n", "colossus_rs_is_buy_list,\n"]}], "source": ["for i in \"\"\"colossus_rs_uniform_spu_id_list_share\n", "colossus_rs_category_1_list\n", "colossus_rs_is_click_list\n", "colossus_rs_category_2_list\n", "colossus_rs_item_id_list_share\n", "colossus_rs_category_3_list\n", "colossus_rs_seller_id_list\n", "colossus_rs_is_buy_list\"\"\".splitlines():\n", "    print(f\"{i},\")"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list array<bigint>,\n", "good_show_hardsearch_exposure_ratio_list array<bigint>,\n", "good_show_hardsearch_item_id_list array<bigint>,\n", "good_show_hardsearch_lagV1_list array<bigint>,\n", "good_show_hardsearch_lagV2_list array<bigint>,\n", "good_show_hardsearch_pagecode_id_list array<bigint>,\n", "good_show_hardsearch_uniform_spu_id_list array<bigint>,\n"]}], "source": ["for i in final_name:\n", "    print(f\"{i} array<bigint>,\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型训练 文件"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [], "source": ["finalname2size_map = {}\n", "for name,slot in name2slot_map.items():\n", "    finalname2size_map[name.replace(prefix, final_prefix)] = size_map[name]\n", "\n", "target = final_name\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## kai feature\n", "目前仅考虑稀疏特征\n", "\n", "注意看category user/photo/combine，写错不影响离线训练，影响上线4\n", "\n", "dragon序列需要去掉slot, size=0, no_default=1"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["class=good_show_hardsearch_count_index_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_exposure_ratio_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_item_id_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_lagV1_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_lagV2_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_pagecode_id_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n", "class=good_show_hardsearch_uniform_spu_id_list, category=combine, field=0, size=0, no_default=1, topic_id=0\n"]}], "source": ["category = \"combine\" # user/photo/combine\n", "\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    print(f\"class={i}, category={category}, field=0, size=0, no_default=1, topic_id=0\")\n", "    slot += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## feature remap\n", "填写dim，没出现的默认dim=4\n", "\n", "结果记得**手动改map_slot**，然后field_adjust.py"]}, {"cell_type": "code", "execution_count": 243, "metadata": {}, "outputs": [{"data": {"text/plain": ["({'good_show_hardsearch_count_index_list': 4,\n", "  'good_show_hardsearch_exposure_ratio_list': 4,\n", "  'good_show_hardsearch_item_id_list': 16,\n", "  'good_show_hardsearch_lagV1_list': 4,\n", "  'good_show_hardsearch_lagV2_list': 4,\n", "  'good_show_hardsearch_pagecode_id_list': 4,\n", "  'good_show_hardsearch_uniform_spu_id_list': 16},\n", " 52)"]}, "execution_count": 243, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dims = [4,4,16,4,4,4,16]\n", "column2dim = {k:all_dims[idx] for idx,k in enumerate(target)}\n", "column2dim, sum(all_dims)"]}, {"cell_type": "code", "execution_count": 244, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["remap_slot=833, dim=4, map_slot=833, size=0, slot=0, name=good_show_hardsearch_count_index_list, category=ad, field=0, feature_size=101, topic_id=0\n", "remap_slot=834, dim=4, map_slot=834, size=0, slot=0, name=good_show_hardsearch_exposure_ratio_list, category=ad, field=0, feature_size=1001, topic_id=0\n", "remap_slot=835, dim=16, map_slot=835, size=0, slot=0, name=good_show_hardsearch_item_id_list, category=ad, field=0, feature_size=10000001, topic_id=0\n", "remap_slot=836, dim=4, map_slot=836, size=0, slot=0, name=good_show_hardsearch_lagV1_list, category=ad, field=0, feature_size=101, topic_id=0\n", "remap_slot=837, dim=4, map_slot=837, size=0, slot=0, name=good_show_hardsearch_lagV2_list, category=ad, field=0, feature_size=10001, topic_id=0\n", "remap_slot=838, dim=4, map_slot=838, size=0, slot=0, name=good_show_hardsearch_pagecode_id_list, category=ad, field=0, feature_size=101, topic_id=0\n", "remap_slot=839, dim=16, map_slot=839, size=0, slot=0, name=good_show_hardsearch_uniform_spu_id_list, category=ad, field=0, feature_size=10000001, topic_id=0\n"]}], "source": ["start_slot = 833\n", "\n", "slot = start_slot\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    dim = column2dim.get(i, 4)\n", "    print(f\"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0\")\n", "    slot += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## kai_v2_model.py\n", "输入adjust之后的第一个field"]}, {"cell_type": "code", "execution_count": 245, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list = kai.new_embedding('good_show_hardsearch_count_index_list', dim=4, slots=[833], expand=100)\n", "good_show_hardsearch_exposure_ratio_list = kai.new_embedding('good_show_hardsearch_exposure_ratio_list', dim=4, slots=[834], expand=100)\n", "good_show_hardsearch_item_id_list = kai.new_embedding('good_show_hardsearch_item_id_list', dim=16, slots=[835], expand=100)\n", "good_show_hardsearch_lagV1_list = kai.new_embedding('good_show_hardsearch_lagV1_list', dim=4, slots=[836], expand=100)\n", "good_show_hardsearch_lagV2_list = kai.new_embedding('good_show_hardsearch_lagV2_list', dim=4, slots=[837], expand=100)\n", "good_show_hardsearch_pagecode_id_list = kai.new_embedding('good_show_hardsearch_pagecode_id_list', dim=4, slots=[838], expand=100)\n", "good_show_hardsearch_uniform_spu_id_list = kai.new_embedding('good_show_hardsearch_uniform_spu_id_list', dim=16, slots=[839], expand=100)\n"]}], "source": ["# 输入adjust之后的第一个field\n", "start_field = 479\n", "seq_len = 100\n", "\n", "slot = start_slot\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    dim = column2dim.get(i, 4)\n", "    # 为了避免mapslot和remapslot重复，需要添加offset\n", "    remap_slot = slot\n", "    map_slot = slot\n", "    kai2_emb = f\"{i} = kai.new_embedding('{i}', dim={dim}, slots=[{remap_slot}], expand={seq_len})\"\n", "    print(kai2_emb)\n", "    start_field += 1\n", "    slot += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来是dnn input"]}, {"cell_type": "code", "execution_count": 246, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list, good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list, good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list\n"]}], "source": ["\n", "#### 接下来是dnn input\n", "all_cols = ', '.join(target)\n", "print(all_cols)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来是model_def"]}, {"cell_type": "code", "execution_count": 247, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list = block_data[100].output\n", "good_show_hardsearch_exposure_ratio_list = block_data[101].output\n", "good_show_hardsearch_item_id_list = block_data[102].output\n", "good_show_hardsearch_lagV1_list = block_data[103].output\n", "good_show_hardsearch_lagV2_list = block_data[104].output\n", "good_show_hardsearch_pagecode_id_list = block_data[105].output\n", "good_show_hardsearch_uniform_spu_id_list = block_data[106].output\n", "good_show_hardsearch_user_embedding = tf.concat([good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list, good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list, good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list], 1)\n", "\n", "\n", " # 这里放到if ln:之后\n", "good_show_hardsearch_user_embedding = tf.contrib.layers.layer_norm(good_show_hardsearch_user_embedding, scope=\"good_show_hardsearch_user_embedding\")\n"]}], "source": ["#### 接下来是model_def\n", "# origin_dnn_input_string = \"\"\"sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,\n", "#             good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,\n", "#             colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,\n", "#             eshop_ad,\n", "#             good_click_cate2cate_cate1_list_extend, good_click_cate2cate_lag_hour_list_extend, good_click_cate2cate_click_type_list_extend, good_click_cate2cate_index_list_extend, \n", "#             cart_photo_exposure_pid_list_expand, cart_photo_exposure_aid_list_expand, cart_photo_exposure_duration_list_expand, cart_photo_exposure_play_time_list_expand, cart_photo_exposure_channel_list_expand, cart_photo_exposure_spu_id_list_expand, cart_photo_exposure_category_list_expand,\n", "#             product_id\"\"\".split(',')\n", "# block_start = len(origin_dnn_input_string)\n", "block_start = 100\n", "# print('origin dnn input len: ', block_start)\n", "for col in target:\n", "    print(f'{col} = block_data[{block_start}].output')\n", "    block_start += 1\n", "\n", "\n", "print(f'good_show_hardsearch_user_embedding = tf.concat([{all_cols}], 1)')\n", "print('\\n\\n # 这里放到if ln:之后')\n", "print(f'good_show_hardsearch_user_embedding = tf.contrib.layers.layer_norm(good_show_hardsearch_user_embedding, scope=\"good_show_hardsearch_user_embedding\")')\n"]}, {"cell_type": "code", "execution_count": 248, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_show_hardsearch_count_index_list = tf.slice(good_show_hardsearch_user_embedding, [0, 0], [-1, 400])\n", "good_show_hardsearch_exposure_ratio_list = tf.slice(good_show_hardsearch_user_embedding, [0, 400], [-1, 400])\n", "good_show_hardsearch_item_id_list = tf.slice(good_show_hardsearch_user_embedding, [0, 800], [-1, 1600])\n", "good_show_hardsearch_lagV1_list = tf.slice(good_show_hardsearch_user_embedding, [0, 2400], [-1, 400])\n", "good_show_hardsearch_lagV2_list = tf.slice(good_show_hardsearch_user_embedding, [0, 2800], [-1, 400])\n", "good_show_hardsearch_pagecode_id_list = tf.slice(good_show_hardsearch_user_embedding, [0, 3200], [-1, 400])\n", "good_show_hardsearch_uniform_spu_id_list = tf.slice(good_show_hardsearch_user_embedding, [0, 3600], [-1, 1600])\n", "\n", "good_show_hardsearch_count_index_list = tf.reshape(good_show_hardsearch_count_index_list, [-1, 100, 4])\n", "good_show_hardsearch_exposure_ratio_list = tf.reshape(good_show_hardsearch_exposure_ratio_list, [-1, 100, 4])\n", "good_show_hardsearch_item_id_list = tf.reshape(good_show_hardsearch_item_id_list, [-1, 100, 16])\n", "good_show_hardsearch_lagV1_list = tf.reshape(good_show_hardsearch_lagV1_list, [-1, 100, 4])\n", "good_show_hardsearch_lagV2_list = tf.reshape(good_show_hardsearch_lagV2_list, [-1, 100, 4])\n", "good_show_hardsearch_pagecode_id_list = tf.reshape(good_show_hardsearch_pagecode_id_list, [-1, 100, 4])\n", "good_show_hardsearch_uniform_spu_id_list = tf.reshape(good_show_hardsearch_uniform_spu_id_list, [-1, 100, 16])\n"]}], "source": ["# good_show_hardsearch_user_embedding是沿着dim*seq维度拼接的，再slice和reshape为各条特征\n", "start_dim = 0\n", "for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{col} = tf.slice(good_show_hardsearch_user_embedding, [0, {start_dim}], [-1, {dim * seq_len}])')\n", "    start_dim += dim * seq_len\n", "    block_start += 1\n", "print()\n", "for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{col} = tf.reshape({col}, [-1, {seq_len}, {dim}])')\n", "    start_dim += dim\n", "    block_start += 1\n"]}, {"cell_type": "code", "execution_count": 249, "metadata": {}, "outputs": [], "source": ["def change_suffix(s, a, b):\n", "    return s.<PERSON><PERSON><PERSON>(a) + b"]}, {"cell_type": "code", "execution_count": 250, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cl_loss += self.ssl_tower_and_cl_loss(good_show_hardsearch_count_index_list, \"good_show_hardsearch_count_index_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_exposure_ratio_list, \"good_show_hardsearch_exposure_ratio_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_item_id_list, \"good_show_hardsearch_item_id_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_lagV1_list, \"good_show_hardsearch_lagV1_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_lagV2_list, \"good_show_hardsearch_lagV2_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_pagecode_id_list, \"good_show_hardsearch_pagecode_id_list_cl\")    +\\\n", "    self.ssl_tower_and_cl_loss(good_show_hardsearch_uniform_spu_id_list, \"good_show_hardsearch_uniform_spu_id_list_cl\")\n", "\n", "good_show_hardsearch_user_embedding = tf.concat([good_show_hardsearch_count_index_list, good_show_hardsearch_exposure_ratio_list, good_show_hardsearch_item_id_list, good_show_hardsearch_lagV1_list, good_show_hardsearch_lagV2_list, good_show_hardsearch_pagecode_id_list, good_show_hardsearch_uniform_spu_id_list], 2)\n"]}], "source": ["cl_loss_str = 'cl_loss += '\n", "str_list = []\n", "for col in target:\n", "    str_list.append(f'self.ssl_tower_and_cl_loss({col}, \"{change_suffix(col, '_list','_list_cl')}\")')\n", "cl_loss_str += '    +\\\\\\n    '.join(str_list)\n", "print(cl_loss_str)\n", "\n", "print()\n", "print(f'good_show_hardsearch_user_embedding = tf.concat([{all_cols}], 2)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["修改建模方式 如 sumpooling或者attn\n", "然后加入dnn_input和soft_dnn_input"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}