"""
Pandas显示配置选项
用于解决terminal过窄时DataFrame显示被省略的问题
"""

import pandas as pd


def configure_pandas_display():
    """配置pandas显示选项，避免内容被省略"""
    
    # === 基础显示设置 ===
    pd.set_option('display.max_columns', None)     # 显示所有列，不省略
    pd.set_option('display.width', None)           # 不限制总显示宽度
    pd.set_option('display.max_colwidth', None)    # 不限制单列最大宽度
    
    # === 行数设置 ===
    pd.set_option('display.max_rows', None)        # 显示所有行（谨慎使用，数据量大时会很长）
    # pd.set_option('display.max_rows', 100)       # 或者设置最大显示行数
    
    # === 精度设置 ===
    pd.set_option('display.precision', 6)          # 浮点数显示精度
    
    # === 换行设置 ===
    pd.set_option('display.expand_frame_repr', False)  # 不自动换行显示
    
    print("✅ Pandas显示配置已设置完成")


def configure_pandas_display_conservative():
    """保守的pandas显示配置（推荐用于生产环境）"""
    
    # 只解决列被省略的问题，保持行数限制
    pd.set_option('display.max_columns', None)     # 显示所有列
    pd.set_option('display.width', None)           # 不限制宽度
    pd.set_option('display.max_colwidth', 50)      # 限制单列最大宽度为50字符
    pd.set_option('display.max_rows', 20)          # 最多显示20行
    pd.set_option('display.expand_frame_repr', False)  # 不换行
    
    print("✅ Pandas保守显示配置已设置完成")


def reset_pandas_display():
    """重置pandas显示选项为默认值"""
    
    pd.reset_option('display.max_columns')
    pd.reset_option('display.width')
    pd.reset_option('display.max_colwidth')
    pd.reset_option('display.max_rows')
    pd.reset_option('display.precision')
    pd.reset_option('display.expand_frame_repr')
    
    print("✅ Pandas显示选项已重置为默认值")


def show_current_pandas_options():
    """显示当前pandas显示选项"""
    
    options = [
        'display.max_columns',
        'display.width', 
        'display.max_colwidth',
        'display.max_rows',
        'display.precision',
        'display.expand_frame_repr'
    ]
    
    print("📊 当前Pandas显示选项:")
    for option in options:
        value = pd.get_option(option)
        print(f"   {option}: {value}")


def demo_dataframe_display():
    """演示不同显示配置的效果"""
    
    # 创建一个宽DataFrame用于测试
    import numpy as np
    
    data = {
        f'column_{i}': np.random.randn(10) for i in range(15)
    }
    df = pd.DataFrame(data)
    
    print("=== 默认显示配置 ===")
    reset_pandas_display()
    print(df)
    
    print("\n=== 完整显示配置 ===")
    configure_pandas_display()
    print(df)
    
    print("\n=== 保守显示配置 ===")
    configure_pandas_display_conservative()
    print(df)


# === 常用的显示方法 ===

def print_df_full(df, title="DataFrame"):
    """完整打印DataFrame，不省略任何内容"""
    
    # 临时设置显示选项
    with pd.option_context('display.max_columns', None,
                          'display.width', None,
                          'display.max_colwidth', None,
                          'display.max_rows', None):
        print(f"\n=== {title} ===")
        print(df)


def print_df_compact(df, title="DataFrame", max_rows=10):
    """紧凑打印DataFrame，适合在窄terminal中查看"""
    
    with pd.option_context('display.max_columns', None,
                          'display.width', 120,
                          'display.max_colwidth', 20,
                          'display.max_rows', max_rows,
                          'display.expand_frame_repr', False):
        print(f"\n=== {title} ===")
        print(df)


def print_df_info(df, title="DataFrame"):
    """打印DataFrame的基本信息和统计"""
    
    print(f"\n=== {title} 信息 ===")
    print(f"形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print(f"数据类型:")
    print(df.dtypes)
    
    if len(df) > 0:
        print(f"\n前5行数据:")
        print_df_compact(df.head(), "前5行")
        
        if len(df) > 5:
            print(f"\n后5行数据:")
            print_df_compact(df.tail(), "后5行")


# === 使用示例 ===

def main():
    """主函数 - 演示各种显示方法"""
    
    print("🔧 Pandas DataFrame显示配置工具")
    print("=" * 50)
    
    # 显示当前配置
    show_current_pandas_options()
    
    # 演示不同配置效果
    demo_dataframe_display()
    
    print("\n📝 使用建议:")
    print("1. 在脚本开头调用 configure_pandas_display_conservative()")
    print("2. 使用 print_df_full() 完整显示重要数据")
    print("3. 使用 print_df_compact() 在窄terminal中查看")
    print("4. 使用 print_df_info() 快速了解数据结构")


if __name__ == "__main__":
    main()
