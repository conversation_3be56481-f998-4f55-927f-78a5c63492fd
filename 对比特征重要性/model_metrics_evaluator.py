"""
模型评估指标计算器
专门用于从训练日志中提取和计算AUC等模型评估指标
"""

import pandas as pd
import numpy as np
import re
from typing import List, Dict, Tuple, Optional, Union


class ModelMetricsEvaluator:
    """模型评估指标计算器"""
    
    # 支持的评估指标
    SUPPORTED_METRICS = [
        'AUC', 'UAUC', 'WUAUC', 'UserNum', 'AllUserNum', 
        'InsNum', 'MAE', 'RMSE', 'LOSS', 'ActualCTR', 'PredictedCTR'
    ]
    
    def __init__(self):
        self.pa = re.compile(r'\%H\#(.*?) ')  # 日期匹配模式
        self.pb = re.compile(r'begin_time=(\w*) ')  # 开始时间匹配模式
    
    def parse_log_file(self, log_path: str, group_by: bool = True) -> Optional[pd.DataFrame]:
        """
        解析单个日志文件，提取模型评估指标
        
        Args:
            log_path: 日志文件路径
            group_by: 是否按测试模式和指标类型分组
            
        Returns:
            包含评估指标的DataFrame，如果解析失败返回None
        """
        if isinstance(log_path, list):
            log_paths = log_path
        else:
            log_paths = [log_path]
            
        all_data = []
        
        for path in log_paths:
            data = self._parse_single_log(path)
            all_data.extend(data)
        
        if not all_data:
            return None
            
        df = pd.DataFrame(all_data)
        
        if group_by:
            key = ['test_mode', 'metric']
            df_sum = df.groupby(key).sum()
            df = df.groupby(key).mean()
            df['speed'] = df_sum['speed']
            df['ins_num'] = df_sum['ins_num']
        
        return df.sort_index()
    
    def _parse_single_log(self, log_path: str) -> List[Dict]:
        """解析单个日志文件的内部方法"""
        data = []
        
        # 初始化状态变量
        ins_num = 1
        speed = 0
        day = ''
        hour = ''
        test_day = ''
        pas = 1
        feature_num = 0
        test_fea_num = 0
        test_mode = False
        
        try:
            with open(log_path, 'r', encoding='utf-8') as fp:
                for line in fp:
                    try:
                        # 判断训练/评估模式
                        if 'begin train' in line or 'end eval' in line:
                            test_mode = False
                        elif 'begin eval' in line:
                            test_mode = True
                            ma = self.pa.search(line)
                            if ma:
                                test_day = ma.group(1).replace('-', '')
                                day = test_day
                                hour = '00'
                        
                        # 解析其他状态信息
                        elif 'eval with dragonfly' in line:
                            wds = line.strip().split(" ")
                            day = wds[-5].split('[')[-1].replace('-', '')
                            hour = wds[-4].split(':')[0]
                        
                        elif 'end_pass: pass_id' in line:
                            wds = line.strip().split(" ")
                            day = wds[-2].split('=')[-1].replace('-', '')
                            hour = wds[-1].split(':')[0]
                        
                        elif 'valid feature num' in line:
                            wds = line.strip().split(" ")
                            test_fea_num = int(wds[-1])
                        
                        elif 'Feature count' in line:
                            wds = line.strip().split(" ")
                            try:
                                feature_num = int(wds[10])
                            except:
                                pass
                        
                        elif 'Instance count:' in line:
                            wds = line.strip().split(" ")
                            ins_num = int(wds[10][:-1])
                        
                        elif '==== begin pass ' in line:
                            wds = line.strip().split(" ")
                            day, hour, pas = wds[11], wds[12], int(wds[13][1:-1])
                        
                        elif 'End updating pass' in line:
                            test_mode = False
                            wds = [w for w in line.strip().split(" ") if w]
                            speed = float(wds[3])
                        
                        elif 'End joining pass' in line:
                            test_mode = True
                            wds = [w for w in line.strip().split(" ") if w]
                            speed = float(wds[3])
                        
                        # 解析评估指标
                        elif ' AUC=' in line:
                            if 'Whole day' in line and 'Train Whole day' not in line:
                                continue
                            
                            metrics_data = self._parse_metrics_line(
                                line, test_mode, ins_num, speed, pas, 
                                test_fea_num if test_mode else feature_num
                            )
                            if metrics_data:
                                data.append(metrics_data)
                    
                    except Exception as e:
                        print(f"解析行时出错: {e}")
                        print(f"问题行: {line}")
                        continue
        
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return []
        
        return data
    
    def _parse_metrics_line(self, line: str, test_mode: bool, ins_num: int, 
                           speed: float, pas: int, feature_num: int) -> Optional[Dict]:
        """解析包含评估指标的行"""
        wds = line.strip().split(" ")
        
        d = {
            'test_mode': test_mode,
            'ins_num': ins_num,
            'speed': speed,
            'pass': pas,
            'feature_num': feature_num
        }
        
        d['metric'] = wds[0]
        
        for wd in wds:
            if len(wd) == 0:
                continue
            if wd[-1] == ':' and len(wd) > 1:
                d['metric'] = wd[:-1]
            elif '=' in wd:
                k, v = wd.split('=')
                try:
                    d[k] = float(v)
                except ValueError:
                    continue
        
        if 'Whole day' in line:
            d['metric'] = 'day_' + d['metric']
        
        return d
    
    def filter_metrics_data(self, df: pd.DataFrame, 
                           test_mode: Optional[bool] = False,
                           exclude_metrics: Optional[List[str]] = None) -> pd.DataFrame:
        """
        过滤评估指标数据
        
        Args:
            df: 包含评估指标的DataFrame
            test_mode: 是否只保留测试模式数据，None表示不过滤
            exclude_metrics: 要排除的指标列表
            
        Returns:
            过滤后的DataFrame
        """
        # 只保留支持的指标列
        available_cols = [col for col in self.SUPPORTED_METRICS if col in df.columns]
        filtered_df = df[available_cols].copy()
        
        # 过滤不包含"tab"的指标
        if 'metric' in df.index.names:
            filtered_df = filtered_df.query('not metric.str.contains("tab")', engine='python')
        
        # 排除特定指标
        if exclude_metrics:
            exclude_set = set(exclude_metrics)
            query_str = f'metric not in {list(exclude_set)}'
            try:
                filtered_df = filtered_df.query(query_str)
            except:
                pass
        
        # 过滤测试模式
        if test_mode is not None:
            try:
                filtered_df = filtered_df.query(f'test_mode=={test_mode}')
            except:
                pass
        
        return filtered_df.dropna()
    
    def calculate_metrics_diff(self, base_df: pd.DataFrame, 
                              exp_df: pd.DataFrame) -> pd.DataFrame:
        """
        计算基线和实验版本之间的指标差异
        
        Args:
            base_df: 基线版本的指标数据
            exp_df: 实验版本的指标数据
            
        Returns:
            包含差异的DataFrame
        """
        available_cols = [col for col in self.SUPPORTED_METRICS 
                         if col in base_df.columns and col in exp_df.columns]
        
        # 计算百分比差异
        diff = ((exp_df[available_cols] - base_df[available_cols]) * 100)
        
        # 计算PCOC (Predicted CTR / Actual CTR)
        if 'PredictedCTR' in available_cols and 'ActualCTR' in available_cols:
            diff["pcoc_base"] = base_df["PredictedCTR"] / base_df["ActualCTR"]
            diff["pcoc_exp"] = exp_df["PredictedCTR"] / base_df["ActualCTR"]
        
        # 对于InsNum，保留原始值而不是差异
        if 'InsNum' in available_cols:
            diff['InsNum'] = base_df['InsNum']
            diff = diff.sort_values(by=['InsNum'], ascending=False)
        
        return diff.dropna()
    
    def compare_experiments(self, base_df: pd.DataFrame, 
                           exp_dfs: List[pd.DataFrame], 
                           metrics: List[str]) -> pd.DataFrame:
        """
        比较多个实验版本与基线版本的指标
        
        Args:
            base_df: 基线版本数据
            exp_dfs: 实验版本数据列表
            metrics: 要比较的指标列表
            
        Returns:
            比较结果DataFrame
        """
        comparison_cols = ["AUC", "AUC_diff", "UAUC", "UAUC_diff", "WUAUC", "WUAUC_diff", 
                          "LOSS", "ActualCTR", "PredictedCTR", "pcoc", "InsNum"]
        
        result_df = pd.DataFrame(columns=comparison_cols)
        
        for metric in metrics:
            i = 0
            for df in [base_df] + exp_dfs:
                metric_cols = ["AUC", "UAUC", "WUAUC", "LOSS", "ActualCTR", "PredictedCTR", "InsNum"]
                
                for k in metric_cols:
                    if k not in df.columns:
                        continue
                        
                    if i == 0:  # 基线版本
                        row_name = f"base_{metric}"
                        try:
                            result_df.loc[row_name, k] = df.loc[(slice(None), metric), k].values[0]
                            result_df.loc[row_name, k + "_diff"] = 0
                        except:
                            continue
                    else:  # 实验版本
                        row_name = f"exp_{i}_{metric}"
                        base_row_name = f"base_{metric}"
                        try:
                            result_df.loc[row_name, k] = df.loc[(slice(None), metric), k].values[0]
                            
                            # 计算差异
                            if base_row_name in result_df.index and k in result_df.columns:
                                base_val = result_df.loc[base_row_name, k]
                                exp_val = result_df.loc[row_name, k]
                                
                                if k in ["AUC", "UAUC"]:
                                    diff_val = (exp_val - base_val) * 100
                                else:
                                    diff_val = exp_val - base_val
                                
                                result_df.loc[row_name, k + "_diff"] = diff_val
                        except:
                            continue
                
                i += 1
        
        # 计算PCOC
        if 'PredictedCTR' in result_df.columns and 'ActualCTR' in result_df.columns:
            result_df["pcoc"] = result_df["PredictedCTR"] / result_df["ActualCTR"]
        
        return result_df


def main():
    """示例用法"""
    evaluator = ModelMetricsEvaluator()
    
    # 解析日志文件
    # base_metrics = evaluator.parse_log_file('path/to/base.log')
    # exp_metrics = evaluator.parse_log_file('path/to/exp.log')
    
    # 过滤数据
    # base_filtered = evaluator.filter_metrics_data(base_metrics, test_mode=False)
    # exp_filtered = evaluator.filter_metrics_data(exp_metrics, test_mode=False)
    
    # 计算差异
    # diff_result = evaluator.calculate_metrics_diff(base_filtered, exp_filtered)
    
    print("模型评估指标计算器已准备就绪")
    print(f"支持的指标: {ModelMetricsEvaluator.SUPPORTED_METRICS}")


if __name__ == "__main__":
    main()
