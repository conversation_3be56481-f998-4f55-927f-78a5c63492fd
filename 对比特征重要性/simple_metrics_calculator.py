"""
简化版模型指标计算器
直接替换原文件中的指标计算逻辑，保持接口兼容性
"""

import pandas as pd
import numpy as np
import re
from pandas import DataFrame


def get_metrics_data(paths, group_by=True):
    """
    从日志文件中提取模型评估指标数据
    
    Args:
        paths: 日志文件路径，可以是字符串或列表
        group_by: 是否按测试模式和指标分组
        
    Returns:
        tuple: (metrics_dataframe, None) - 只返回指标数据，不包含特征重要性
    """
    if not isinstance(paths, list):
        paths = [paths]
    
    # 正则表达式模式
    pa = re.compile(r'\%H\#(.*?) ')
    pb = re.compile(r'begin_time=(\w*) ')
    
    data = []
    ins_num = 1
    df_all = DataFrame()
    
    for path in paths:
        try:
            fp = open(path, 'r', encoding='utf-8')
            data = []
            
            # 状态变量
            speed = 0
            day = ''
            hour = ''
            test_day = ''
            pas = 1
            feature_num = 0
            test_fea_num = 0
            test_mode = False
            
            for line in fp.readlines():
                try:
                    # 判断训练/评估模式
                    if 'begin train' in line or 'end eval' in line:
                        test_mode = False
                    elif 'begin eval' in line:
                        test_mode = True
                        ma = pa.search(line)
                        if ma:
                            test_day = ma.group(1).replace('-', '')
                            day = test_day
                            hour = '00'
                    
                    # 解析其他信息
                    elif 'eval with dragonfly' in line:
                        wds = line.strip().split(" ")
                        day = wds[-5].split('[')[-1].replace('-', '')
                        hour = wds[-4].split(':')[0]
                    
                    elif 'end_pass: pass_id' in line:
                        wds = line.strip().split(" ")
                        day = wds[-2].split('=')[-1].replace('-', '')
                        hour = wds[-1].split(':')[0]
                    
                    elif 'valid feature num' in line:
                        wds = line.strip().split(" ")
                        test_fea_num = int(wds[-1])
                    
                    elif 'Feature count' in line:
                        wds = line.strip().split(" ")
                        try:
                            feature_num = int(wds[10])
                        except:
                            pass
                    
                    elif 'Instance count:' in line:
                        wds = line.strip().split(" ")
                        ins_num = int(wds[10][:-1])
                    
                    elif '==== begin pass ' in line:
                        wds = line.strip().split(" ")
                        day, hour, pas = wds[11], wds[12], int(wds[13][1:-1])
                    
                    elif 'End updating pass' in line:
                        test_mode = False
                        wds = [w for w in line.strip().split(" ") if w]
                        speed = float(wds[3])
                    
                    elif 'End joining pass' in line:
                        test_mode = True
                        wds = [w for w in line.strip().split(" ") if w]
                        speed = float(wds[3])
                    
                    # 解析评估指标 - 核心逻辑
                    elif ' AUC=' in line:
                        if 'Whole day' in line and 'Train Whole day' not in line:
                            continue
                        
                        wds = line.strip().split(" ")
                        d = {
                            'test_mode': test_mode,
                            'ins_num': ins_num,
                            'speed': speed,
                            'pass': pas,
                            'feature_num': test_fea_num if test_mode else feature_num
                        }
                        d['metric'] = wds[0]
                        
                        for wd in wds:
                            if len(wd) == 0:
                                continue
                            if wd[-1] == ':' and len(wd) > 1:
                                d['metric'] = wd[:-1]
                            elif '=' in wd:
                                k, v = wd.split('=')
                                d[k] = float(v)
                        
                        if 'Whole day' in line:
                            d['metric'] = 'day_' + d['metric']
                        
                        data.append(d)
                
                except Exception as e:
                    print(f"解析行出错: {e}")
                    print(f"问题行: {line}")
                    continue
            
            fp.close()
            
        except Exception as e:
            print(f"读取文件出错: {e}")
            continue
        
        if len(data) == 0:
            continue
        
        df = DataFrame(data)
        key = ['test_mode', 'metric']
        
        if group_by:
            df_sum = df.groupby(key).sum()
            df = df.groupby(key).mean()
            df['speed'], df['ins_num'] = df_sum['speed'], df_sum['ins_num']
        
        df_all = pd.concat([df_all, df])
    
    if df_all.empty:
        return None, None
    
    if group_by:
        key = ['test_mode', 'metric']
        df_all = df_all.groupby(key).mean()
    
    return df_all.sort_index(), None


def filter_metrics_data(d):
    """
    过滤指标数据，保持与原函数兼容
    
    Args:
        d: 包含指标的DataFrame
        
    Returns:
        过滤后的DataFrame
    """
    cols = 'AUC	UAUC	WUAUC	UserNum	AllUserNum	InsNum	MAE	RMSE	LOSS	ActualCTR	PredictedCTR'.split()
    
    # 只保留存在的列
    available_cols = [col for col in cols if col in d.columns]
    d = d[available_cols]
    
    # 过滤条件
    d = d.query('not metric.str.contains("tab")', engine='python')
    m = {'ctl1'}
    q = 'metric not in @m'
    q += ' and test_mode==False'  # 只保留训练模式数据
    d = d.query(q)
    d = d.dropna()
    
    return d


def calculate_metrics_difference(d1, d2):
    """
    计算两个指标数据集之间的差异
    
    Args:
        d1: 基线数据
        d2: 实验数据
        
    Returns:
        包含差异的DataFrame
    """
    cols = 'AUC	UAUC	WUAUC	UserNum	AllUserNum	InsNum	MAE	RMSE	LOSS	ActualCTR	PredictedCTR'.split()
    
    # 只使用两个数据集都有的列
    available_cols = [col for col in cols if col in d1.columns and col in d2.columns]
    
    # 计算百分比差异
    diff = ((d2[available_cols] - d1[available_cols]) * 100)
    
    # 计算PCOC (Predicted CTR over Click Rate)
    if 'PredictedCTR' in available_cols and 'ActualCTR' in available_cols:
        diff["pcoc_base"] = d1["PredictedCTR"] / d1["ActualCTR"]
        diff["pcoc_exp"] = d2["PredictedCTR"] / d1["ActualCTR"]
    
    # 对于InsNum，保留原始值
    if 'InsNum' in available_cols:
        diff['InsNum'] = d1['InsNum']
        diff = diff.sort_values(by=['InsNum'], ascending=False)
    
    diff = diff.dropna()
    return diff


def compare_multiple_experiments(base, exp_list, metrics):
    """
    比较多个实验与基线的性能
    
    Args:
        base: 基线数据
        exp_list: 实验数据列表
        metrics: 要比较的指标列表
        
    Returns:
        比较结果DataFrame
    """
    cols = ["AUC", "AUC_diff", "UAUC", "UAUC_diff", "WUAUC", "WUAUC_diff", 
            "LOSS", "ActualCTR", "PredictedCTR", "pcoc", "InsNum"]
    
    n = len(exp_list)
    df = pd.DataFrame(None, columns=cols)
    
    for metric in metrics:
        i = 0
        for d in [base] + exp_list:
            for k in ["AUC", "UAUC", "WUAUC", "LOSS", "ActualCTR", "PredictedCTR", "InsNum"]:
                if k not in d.columns:
                    continue
                
                try:
                    if i % len([base] + exp_list) == 0:
                        # 基线数据
                        row_name = f"base_{metric}"
                        df.loc[row_name, k] = d.loc[(slice(None), metric), k].values[0]
                    else:
                        # 实验数据
                        exp_row_name = f"exp_{i}_{metric}"
                        base_row_name = f"base_{metric}"
                        
                        df.loc[exp_row_name, k] = d.loc[(slice(None), metric), k].values[0]
                        
                        # 计算差异
                        if base_row_name in df.index:
                            base_val = df.loc[base_row_name, k]
                            exp_val = df.loc[exp_row_name, k]
                            
                            df.at[base_row_name, k + "_diff"] = 0
                            
                            if k in ["AUC", "UAUC"]:
                                diff_val = (exp_val - base_val) * 100
                            else:
                                diff_val = exp_val - base_val
                            
                            df.at[exp_row_name, k + "_diff"] = diff_val
                
                except Exception as e:
                    print(f"处理指标 {k} 时出错: {e}")
                    continue
            
            i += 1
    
    # 计算PCOC
    if 'PredictedCTR' in df.columns and 'ActualCTR' in df.columns:
        df["pcoc"] = df["PredictedCTR"] / df["ActualCTR"]
    
    return df


# 为了保持向后兼容性，提供原函数名的别名
get_data = get_metrics_data
filter_data = filter_metrics_data
diff = calculate_metrics_difference
compare_ctl = compare_multiple_experiments


def main():
    """示例使用"""
    print("简化版模型指标计算器")
    print("主要功能:")
    print("1. get_metrics_data() - 从日志提取指标")
    print("2. filter_metrics_data() - 过滤指标数据")
    print("3. calculate_metrics_difference() - 计算差异")
    print("4. compare_multiple_experiments() - 多实验比较")
    
    # 支持的指标
    metrics = ['AUC', 'UAUC', 'WUAUC', 'UserNum', 'AllUserNum', 
               'InsNum', 'MAE', 'RMSE', 'LOSS', 'ActualCTR', 'PredictedCTR']
    print(f"\n支持的指标: {metrics}")


if __name__ == "__main__":
    main()
