import sys
if '/home/<USER>' not in sys.path:
    sys.path.append('/home/<USER>')
# from pylib.common import *
from pandas import *
import pandas as pd
import re
import numpy as np
# remote = RemoteRunner('<EMAIL>','reco-krp_l3/pub/')
# remote = RemoteRunner('<EMAIL>','krp_l3')

import cufflinks as cf
cf.set_config_file(offline=True)
# jup_set_show_all()

path_exp = '/Users/<USER>/Downloads/exp-importance-0607.log'
path_base = '/Users/<USER>/Downloads/base-importance-0607.log'

ins_num=1
def get_data(paths, group_by=True):
    if not isinstance(paths,list):
        paths=[paths]
    pa=re.compile(r'\%H\#(.*?) ')
    pb=re.compile(r'begin_time=(\w*) ')
    data=[]
    fea_data=[]
    speed=0
    day=''
    hour=''
    test_day=''
    pas=1
    feature_num=0
    test_fea_num=0
    test_mode=False
    df_all=DataFrame()
    for path in paths:
        fp = open(path)
        data=[]
        for line in fp.readlines():
            try:
                if 'begin train' in line or 'end eval' in line:
                    test_mode=False
                elif 'begin eval' in line:
                    test_mode=True
                    wds=line.strip().split(" ")
                    ma=pa.search(line)
                    if ma:
                        test_day=ma.group(1).replace('-','')
                        day=test_day
                        hour='00'
                        print(line, day)
                    mb=pb.search(line)
#                     if mb:
#                         print(mb.group(1))
                elif 'eval with dragonfly' in line:
                    wds=line.strip().split(" ")
                    day=wds[-5].split('[')[-1].replace('-','')
                    hour=wds[-4].split(':')[0]
                    print('eval day=', day, 'hour=', hour)
                elif 'end_pass: pass_id' in line:
                    wds=line.strip().split(" ")
                    day=wds[-2].split('=')[-1].replace('-','')
                    hour=wds[-1].split(':')[0]
#                     if test_mode:
#                         print('day=', day, 'hour=', hour, 'test_mode=', test_mode)
                
                elif 'valid feature num' in line:
                    wds=line.strip().split(" ")
                    test_fea_num = int(wds[-1])
                elif 'Feature count' in line:
                    wds=line.strip().split(" ")
                    try:
                        feature_num = int(wds[10])
                    except:
                        pass
                elif 'Instance count:' in line:
                    wds=line.strip().split(" ")
                    ins_num = int(wds[10][:-1])
                elif '==== begin pass ' in line:
                    wds=line.strip().split(" ")
                    day,hour,pas=wds[11], wds[12], int(wds[13][1:-1])
                elif 'End updating pass' in line:
                    test_mode=False
                    wds=[w  for w in line.strip().split(" ") if w]
                    speed=float(wds[3])
                elif 'End joining pass' in line:
                    test_mode=True
                    wds=[w  for w in line.strip().split(" ") if w]
                    speed=float(wds[3])
                elif (' AUC=' in line) :
                    
                    if 'Whole day' in line and 'Train Whole day' not in line :
                        continue
                    wds=line.strip().split(" ")
                    d={'test_mode':test_mode, 'ins_num':ins_num, 'speed': speed, 'pass':pas, 'feature_num': test_fea_num if test_mode else feature_num }
                    d['metric']=wds[0]
                    for wd in wds:
                        if len(wd)==0:
                            continue
                        if wd[-1]==':' and len(wd)>1:
                            d['metric']=wd[:-1]
                        elif '=' in wd:
                            k,v=wd.split('=')
                            d[k]=float(v)
#                     if test_mode:
#                         print(line) 
#                         print(d['metric'])
                    if 'Whole day' in line:
                        d['metric']='day_'+d['metric']
#                         print(line)
                    data.append(d)
                elif ' stdout first_stage_ctcvr__' in line and ' : ' in line:
                    tmp_list = line.strip().replace(':', '').split()[2:]
                    importance_map = {"feature_name": tmp_list[0].replace('first_stage_ctcvr__', ''), 'slot': np.NaN if '_slot' not in tmp_list else tmp_list[tmp_list.index('_slot')+1], 
                                      'pos': np.NaN if '_pos' not in tmp_list else tmp_list[tmp_list.index('_pos')+1], 'grad': tmp_list[-1]}
                    fea_data.append(importance_map)
                    
            except Exception as e:
                print(e)
                print(line)
                pass
#                 raise e
            fp.close()
        if len(data)==0 or fea_data==0:
            continue
        df=DataFrame(data)
        feature_importance=DataFrame(fea_data)
        key=['test_mode', 'metric']
        if group_by:
            df_sum=df.groupby(key).sum()
            df=df.groupby(key).mean()
            df['speed'],df['ins_num']=df_sum['speed'], df_sum['ins_num']
        df_all=pd.concat([df_all, df])
            #groupby(key).mean()
    if  df_all.columns.empty or feature_importance.columns.empty:
        return None, None
    if group_by:
        df_all=df_all.groupby(key).mean()
    return df_all.sort_index(), feature_importance


cols='AUC	UAUC	WUAUC	UserNum	AllUserNum	InsNum	MAE	RMSE	LOSS	ActualCTR	PredictedCTR'.split()
print(cols)

# def _color_red_or_green(val):
#     if val>0 :
#         a= f'background-color: hsl(0, 100%, {max(40,100-val*40)}%)'
#         return a
#     if val<0: 
#         a= f'background-color: hsl(120, 100%, {max(40,100+val*40)}%)'
#         return a
#         return 'color: green'
    
#     return ''

def apply_formatting(col):
    return ['background-color: red' if c > 0 else 'background-color: green' for c in col.values]
    
def diff(d1, d2):
    diff=((d2[cols]-d1[cols])*100 )#.apply(lambda x : x *(x.abs()>=1 ))
    diff["pcoc_base"] = d1["PredictedCTR"]/d1["ActualCTR"]
    diff["pcoc_exp"] = d2["PredictedCTR"]/d1["ActualCTR"]

    for col in cols:
        if col == "InsNum":
            diff[col] = d1[col]
            diff = diff.sort_values(by=[col], ascending=False)
#     diff.rename(columns=lambda x: x+'_diff', inplace=True)
    diff= diff.dropna()
    
#     diff.style.apply(apply_formatting)
    return diff

def filter_data(d):
    d= d[cols]
    d=d.query('not metric.str.contains("tab")')
    m={'ctl1'}
    q=''
    q+=' metric not in @m'
#     q+=' and test_mode==True'
    q += ' and test_mode==False'
    d=d.query(q)
    d=d.dropna()
    return d

def compare_ctl(base, exp_list, metrics):
    cols = ["AUC", "AUC_diff", "UAUC", "UAUC_diff", "WUAUC","WUAUC_diff", "LOSS", "ActualCTR", "PredictedCTR", "pcoc", "InsNum"]
    n = len(exp_list)
    df = pd.DataFrame(None, columns=cols)
    for metric in metrics:
        i = 0
        for d in [base]+exp_list:
            for k in ["AUC", "UAUC", "WUAUC", "LOSS", "ActualCTR", "PredictedCTR", "InsNum"]:            
                if i % len([base]+exp_list)==0:
                    df.append(pd.Series(name = "base"))
                    df.loc["base_"+metric, k] = d.loc[(slice(None), metric), k].values[0]
                else:
                    df.append(pd.Series(name = 'exp_'+str(i)))
                    df.loc["exp_"+str(i)+"_"+metric, k] = d.loc[(slice(None), metric), k].values[0]

                    df.at["base_"+metric, k+"_diff"] = 0
                    df.at["exp_"+str(i)+"_"+metric, k+"_diff"] = (df.at["exp_"+str(i)+"_"+metric, k]-df.at["base_"+metric, k])*100 if k in ["AUC", "AUC_diff", "UAUC"] else (df.at["exp_"+str(i)+"_"+metric, k]-df.at["base_"+metric, k])
            
            i += 1
    df["pcoc"] = df["PredictedCTR"]/df["ActualCTR"] 

    return df


# base, base_fea_importance= get_data(path)
# base = filter_data(base)

print(1)
exp, exp_fea_importance = get_data(path_exp)
print(type(exp))
print(type(exp_fea_importance))
exp = filter_data(exp)
base, base_fea_importance= get_data(path_base)
base = filter_data(base)

# pd.set_option('display.max_rows', None)
# metrics = [each[1] for each in list(base.index)]
# df = compare_ctl(base, [exp], metrics) #
# # display(df)
# # df

exp_fea_importance.to_csv('exp_fea_importance.csv')
base_fea_importance.to_csv('base_fea_importance.csv')
# df.to_csv('perfomance.csv')

# exp_fea_importance.head()


base_fea_importance = pd.read_csv('base_fea_importance.csv', names=['feature_name', 'slot', 'pos', 'grad'], header=1)
exp_fea_importance = pd.read_csv('exp_fea_importance.csv', names=['feature_name', 'slot', 'pos', 'grad'], header=0)

# base_fea_importance = pd.read_csv('./base_fea_importance.csv', names=['feature_name', 'slot', 'pos', 'grad'], header=1)
# exp_fea_importance = pd.read_csv('/home/<USER>/sumpooling_attention/exp_fea_importance.csv', names=['feature_name', 'slot', 'pos', 'grad'], header=0)

def refactor_col_name(x):
    if '_ids' in x['feature_name']:
        x['feature_name'] = x['feature_name'].replace('_ids', '_input_id')
    if '_cats' in x['feature_name']:
        x['feature_name'] = x['feature_name'].replace('_cats', '_input_info')
    return x
base_fea_importance = base_fea_importance.apply(lambda x: refactor_col_name(x), axis=1)
# base_fea_importance
base_fea_set = set(base_fea_importance['feature_name'].tolist())
exp_fea_importance = exp_fea_importance.apply(lambda x: refactor_col_name(x), axis=1)
exp_fea_set = set(exp_fea_importance['feature_name'].tolist())


print(base_fea_importance.dtypes)
print(base_fea_importance.shape)
print(exp_fea_importance.dtypes)
print(exp_fea_importance.shape)
total_importance = pd.merge(exp_fea_importance, base_fea_importance, on=['feature_name', 'slot', 'pos'], how='outer')
print(total_importance.dtypes)
print(total_importance.shape)
# total_importance = exp_fea_importance
# total_importance['grad_exp'] = total_importance['grad'].astype(float)

total_importance['grad_exp'] = total_importance['grad_x'].astype(float)
total_importance['grad_base'] = total_importance['grad_y'].astype(float)
total_importance['grad_diff'] = total_importance['grad_exp'].astype(float)- total_importance['grad_base'].astype(float)
total_importance=total_importance.drop(['grad_x', 'grad_y'], axis=1)

total_importance['grad_exp_rank'] = total_importance['grad_exp'].rank(ascending=False,na_option='bottom').astype(int)
total_importance['grad_base_rank'] = total_importance['grad_base'].rank(ascending=False,na_option='bottom').astype(int)
total_importance['grad_diff_rank'] = total_importance['grad_diff'].rank(ascending=False,na_option='bottom').astype(int)

# name_set = total_importance['feature_name'].tolist()
# by_name = pd.DataFrame(columns=['feature_name', 'grad_exp', 'grad_base', 'grad_diff'])
# for feat_name in set(name_set):
#     by_name = by_name.append(total_importance[(total_importance['feature_name']==feat_name) & (total_importance['slot'].isna()) & (total_importance['pos'].isna())].drop(['grad_exp_rank', 'grad_base_rank', 'grad_diff_rank'], axis=1))
# by_name = by_name.drop(['slot', 'pos'], axis=1).reset_index(drop=True)
# print(type(by_name))
# # by_name['grad_base'] = by_name['grad_base'].fillna(0).to_numeric(by_name['grad_base'])
# by_name['grad_exp_rank'] = by_name['grad_exp'].astype(float).rank(ascending=False,na_option='bottom').astype(int)
# by_name['grad_base_rank'] = by_name['grad_base'].astype(float).rank(ascending=False,na_option='bottom').astype(int)
# by_name['grad_diff_rank'] = by_name['grad_diff'].astype(float).rank(ascending=False,na_option='bottom').astype(int)

total_importance.to_csv('total_importance_exp.csv')
# by_name.to_csv('by_name_exp.csv')

# by_name.sort_values('grad_diff', ascending=False)

# notna