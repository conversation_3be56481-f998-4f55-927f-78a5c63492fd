import re
import csv

def parse_log_file(log_file_path, output_csv_path):
    """
    解析日志文件，提取feature name、slot和importance值
    如果feature name和slot相同，覆盖之前的结果
    按importance降序排序并导出为CSV
    """

    # 使用字典存储数据，key为(feature_name, slot)的元组，value为importance
    # 这样可以自动覆盖相同feature_name和slot的记录
    feature_data = {}

    # 打开日志文件
    with open(log_file_path, 'r') as file:
        # 读取第一个"after pass run start"之后的内容
        found_start = False
        for line in file:
            if 'after pass run start' in line:
                found_start = True
                break

        if not found_start:
            print("未找到 'after pass run start' 标记")
            return

        # 继续读取后续行
        for line in file:
            # 匹配格式: stdout first_stage_ctcvr__sparse16 _slot 625 : 	 7.566834416287312e-07
            match = re.search(r'stdout\s+(.+?)__(\w+)\s+_slot\s+(\d+)\s*:\s+([\d.e-]+)', line)
            if match:
                # prefix = match.group(1)  # first_stage_ctcvr (暂时不使用)
                feature_name = match.group(2)  # sparse16
                slot = int(match.group(3))  # 625
                importance = float(match.group(4))  # 7.566834416287312e-07

                # 使用(feature_name, slot)作为key，如果重复会自动覆盖
                key = (feature_name, slot)
                feature_data[key] = importance

                print(f"提取到: feature_name={feature_name}, slot={slot}, importance={importance}")

    if not feature_data:
        print("未找到匹配的数据")
        return

    # 转换为三元组列表并按importance降序排序
    result_data = [(feature_name, slot, importance)
                   for (feature_name, slot), importance in feature_data.items()]
    result_data.sort(key=lambda x: x[2], reverse=True)

    # 导出为CSV
    with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        # 写入表头
        writer.writerow(['feature_name', 'slot', 'importance'])
        # 写入数据
        writer.writerows(result_data)

    print(f"\n总共提取到 {len(result_data)} 条记录")
    print(f"结果已导出到: {output_csv_path}")

    # 显示前10条记录
    print("\n前10条记录（按importance降序）:")
    for i, (feature_name, slot, importance) in enumerate(result_data[:10], 1):
        print(f"{i}. feature_name: {feature_name}, slot: {slot}, importance: {importance}")

    return result_data

# 使用示例
if __name__ == "__main__":
    log_file_path = '/Users/<USER>/Downloads/exp-importance-0607.log'
    output_csv_path = 'feature_importance_results.csv'

    results = parse_log_file(log_file_path, output_csv_path)