{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["function cal_price(ori_price)\n", "                        local res = 0\n", "                        res = ori_price // 100\n", "                        if res >= 300 then res = 300\n", "                        end\n", "                        if res <0 then res = 0\n", "                        end\n", "                        return res\n", "                    end\n", "\n", "                    function cal_diff_price(ori_price, price)\n", "                        if ori_price == 0 then return 0\n", "                        end\n", "                        local slice = 100\n", "                        local res = ori_price - price\n", "                        res = res * slice // ori_price\n", "                        if res <0 then res = 0\n", "                        end\n", "                        if res >slice then res = slice\n", "                        end\n", "                        return res\n", "                    end\n", "\n", "                    function cal_view_time(view_time)\n", "                        local res = view_time\n", "                        if res <0 then res = 0\n", "                        end\n", "                        if res > 60 then res = 60\n", "                        end\n", "                        return res\n", "                    end\n", "\n", "                    function cal_cross(item1, item2)\n", "                        return tostring(item1)..\"_\"..tostring(item2)\n", "                    end\n", "\n", "                    function calculate()\n", "                        local good_click_item_id_list = good_click_item_id_list or {}\n", "                        local good_click_seller_id_list = good_click_seller_id_list or {}\n", "                        local good_click_real_seller_id_list = good_click_real_seller_id_list or {}\n", "                        local good_click_timestamp_list = good_click_timestamp_list or {}\n", "                        local good_click_category_list = good_click_category_list or {}\n", "                        local good_click_flow_type_list = good_click_flow_type_list or {}\n", "                        local good_click_from_list = good_click_from_list or {}\n", "                        local good_click_price_list = good_click_price_list or {}\n", "                        local good_detail_page_view_time_list = good_detail_page_view_time_list or {}\n", "                        local good_click_origin_price_list = good_click_origin_price_list or {}\n", "                        local good_click_label_list = good_click_label_list or {}\n", "\n", "                        local new_good_click_item_id_list_extend = {}\n", "                        local new_good_click_seller_id_list_extend = {}\n", "                        local new_good_click_real_seller_id_list_extend = {}\n", "                        local new_good_click_lag_list_extend = {}\n", "                        local new_good_click_cate1_list_extend = {}\n", "                        local new_good_click_cate2_list_extend = {}\n", "                        local new_good_click_cate3_list_extend = {}\n", "                        local new_good_click_carry_type_list_extend = {}\n", "                        local new_good_click_click_type_list_extend = {}\n", "                        local new_good_click_from_list_extend = {}\n", "                        local new_good_click_price_list_extend = {}\n", "                        local new_good_click_origin_price_list_extend = {}\n", "                        local new_good_click_page_view_time_list_extend = {}\n", "                        local new_good_click_price_diff_list_extend = {}\n", "                        local new_good_click_label_list_extend = {}\n", "                        local new_good_click_index_list_extend = {}\n", "\n", "                        local new_good_click_lag_hour_list_extend = {}\n", "                        local new_good_click_lag_min_list_extend = {}\n", "                        local new_good_click_seller_id_lag_list_extend = {}\n", "                        local new_good_click_cate1_lag_list_extend = {}\n", "                        local new_good_click_cate2_lag_list_extend = {}\n", "                        local new_good_click_cate3_lag_list_extend = {}\n", "                        local new_good_click_seller_id_price_list_extend = {}\n", "                        local new_good_click_cate1_price_list_extend = {}\n", "                        local new_good_click_cate2_price_list_extend = {}\n", "                        local new_good_click_cate3_price_list_extend = {}\n", "                        local new_good_click_seller_id_view_list_extend = {}\n", "                        local new_good_click_cate1_view_list_extend = {}\n", "                        local new_good_click_cate2_view_list_extend = {}\n", "                        local new_good_click_cate3_view_list_extend = {}\n", "\n", "                        local count = 0\n", "                        local len = #good_click_item_id_list\n", "                        for index=len,1,-1 do\n", "                table.insert(new_good_click_item_id_list_extend, good_click_item_id_list[index])\n", "                            table.insert(new_good_click_seller_id_list_extend, good_click_seller_id_list[index])\n", "                            table.insert(new_good_click_real_seller_id_list_extend, good_click_real_seller_id_list[index])\n", "                            table.insert(new_good_click_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24))\n", "                            table.insert(new_good_click_cate1_list_extend, (good_click_category_list[index] >> 48) & 0xffff)\n", "                            table.insert(new_good_click_cate2_list_extend, (good_click_category_list[index] >> 32) & 0xffff)\n", "                            table.insert(new_good_click_cate3_list_extend, (good_click_category_list[index] >> 16) & 0xffff)\n", "                            table.insert(new_good_click_carry_type_list_extend, (good_click_flow_type_list[index]>>16) & 0xff)\n", "                            table.insert(new_good_click_click_type_list_extend, (good_click_flow_type_list[index]>>24) & 0xff)\n", "                            table.insert(new_good_click_from_list_extend, good_click_from_list[index])\n", "                            table.insert(new_good_click_price_list_extend, cal_price(good_click_price_list[index]))\n", "                            table.insert(new_good_click_origin_price_list_extend, cal_price(good_click_origin_price_list[index]))\n", "                            table.insert(new_good_click_price_diff_list_extend, cal_diff_price(good_click_origin_price_list[index], good_click_price_list[index]))\n", "\n", "                            table.insert(new_good_click_page_view_time_list_extend, cal_view_time(good_detail_page_view_time_list[index]))\n", "                            table.insert(new_good_click_label_list_extend, good_click_label_list[index])\n", "                            table.insert(new_good_click_index_list_extend, count)\n", "\n", "                            table.insert(new_good_click_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600))\n", "                            table.insert(new_good_click_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(60))\n", "                            \n", "                            table.insert(new_good_click_seller_id_lag_list_extend, cal_cross(good_click_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\n", "                            table.insert(new_good_click_cate1_lag_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff,\n", "                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\n", "                            table.insert(new_good_click_cate2_lag_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff,\n", "                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\n", "                            table.insert(new_good_click_cate3_lag_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff,\n", "                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\n", "                            table.insert(new_good_click_seller_id_price_list_extend, cal_cross(good_click_seller_id_list[index], cal_price(good_click_origin_price_list[index])))\n", "                            table.insert(new_good_click_cate1_price_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_price(good_click_origin_price_list[index])))\n", "                            table.insert(new_good_click_cate2_price_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_price(good_click_origin_price_list[index])))\n", "                            table.insert(new_good_click_cate3_price_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_price(good_click_origin_price_list[index])))\n", "                            table.insert(new_good_click_seller_id_view_list_extend, cal_cross(good_click_seller_id_list[index], cal_view_time(good_detail_page_view_time_list[index])))\n", "                            table.insert(new_good_click_cate1_view_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\n", "                            table.insert(new_good_click_cate2_view_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\n", "                            table.insert(new_good_click_cate3_view_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\n", "\n", "                            count = count + 1\n", "                            if count >= 300 then\n", "                                break\n", "         end\n", "            end\n", "\n", "                        return new_good_click_item_id_list_extend, new_good_click_seller_id_list_extend, new_good_click_real_seller_id_list_extend, new_good_click_lag_list_extend,\n", "                        new_good_click_cate1_list_extend, new_good_click_cate2_list_extend,\n", "                        new_good_click_cate3_list_extend, new_good_click_carry_type_list_extend, new_good_click_click_type_list_extend,\n", "                        new_good_click_from_list_extend, new_good_click_price_list_extend,\n", "                        new_good_click_origin_price_list_extend, new_good_click_price_diff_list_extend, new_good_click_page_view_time_list_extend, new_good_click_label_list_extend,\n", "                        new_good_click_index_list_extend,\n", "                        new_good_click_lag_hour_list_extend, new_good_click_lag_min_list_extend,\n", "                        new_good_click_seller_id_lag_list_extend, new_good_click_cate1_lag_list_extend, new_good_click_cate2_lag_list_extend, new_good_click_cate3_lag_list_extend,\n", "                        new_good_click_seller_id_price_list_extend, new_good_click_cate1_price_list_extend, new_good_click_cate2_price_list_extend, new_good_click_cate3_price_list_extend,\n", "                        new_good_click_seller_id_view_list_extend, new_good_click_cate1_view_list_extend, new_good_click_cate2_view_list_extend, new_good_click_cate3_view_list_extend\n", "                    end\",\n", "\n"]}], "source": ["print(\"\"\"function cal_price(ori_price)\\n                        local res = 0\\n                        res = ori_price // 100\\n                        if res >= 300 then res = 300\\n                        end\\n                        if res <0 then res = 0\\n                        end\\n                        return res\\n                    end\\n\\n                    function cal_diff_price(ori_price, price)\\n                        if ori_price == 0 then return 0\\n                        end\\n                        local slice = 100\\n                        local res = ori_price - price\\n                        res = res * slice // ori_price\\n                        if res <0 then res = 0\\n                        end\\n                        if res >slice then res = slice\\n                        end\\n                        return res\\n                    end\\n\\n                    function cal_view_time(view_time)\\n                        local res = view_time\\n                        if res <0 then res = 0\\n                        end\\n                        if res > 60 then res = 60\\n                        end\\n                        return res\\n                    end\\n\\n                    function cal_cross(item1, item2)\\n                        return tostring(item1)..\\\"_\\\"..tostring(item2)\\n                    end\\n\\n                    function calculate()\\n                        local good_click_item_id_list = good_click_item_id_list or {}\\n                        local good_click_seller_id_list = good_click_seller_id_list or {}\\n                        local good_click_real_seller_id_list = good_click_real_seller_id_list or {}\\n                        local good_click_timestamp_list = good_click_timestamp_list or {}\\n                        local good_click_category_list = good_click_category_list or {}\\n                        local good_click_flow_type_list = good_click_flow_type_list or {}\\n                        local good_click_from_list = good_click_from_list or {}\\n                        local good_click_price_list = good_click_price_list or {}\\n                        local good_detail_page_view_time_list = good_detail_page_view_time_list or {}\\n                        local good_click_origin_price_list = good_click_origin_price_list or {}\\n                        local good_click_label_list = good_click_label_list or {}\\n\\n                        local new_good_click_item_id_list_extend = {}\\n                        local new_good_click_seller_id_list_extend = {}\\n                        local new_good_click_real_seller_id_list_extend = {}\\n                        local new_good_click_lag_list_extend = {}\\n                        local new_good_click_cate1_list_extend = {}\\n                        local new_good_click_cate2_list_extend = {}\\n                        local new_good_click_cate3_list_extend = {}\\n                        local new_good_click_carry_type_list_extend = {}\\n                        local new_good_click_click_type_list_extend = {}\\n                        local new_good_click_from_list_extend = {}\\n                        local new_good_click_price_list_extend = {}\\n                        local new_good_click_origin_price_list_extend = {}\\n                        local new_good_click_page_view_time_list_extend = {}\\n                        local new_good_click_price_diff_list_extend = {}\\n                        local new_good_click_label_list_extend = {}\\n                        local new_good_click_index_list_extend = {}\\n\\n                        local new_good_click_lag_hour_list_extend = {}\\n                        local new_good_click_lag_min_list_extend = {}\\n                        local new_good_click_seller_id_lag_list_extend = {}\\n                        local new_good_click_cate1_lag_list_extend = {}\\n                        local new_good_click_cate2_lag_list_extend = {}\\n                        local new_good_click_cate3_lag_list_extend = {}\\n                        local new_good_click_seller_id_price_list_extend = {}\\n                        local new_good_click_cate1_price_list_extend = {}\\n                        local new_good_click_cate2_price_list_extend = {}\\n                        local new_good_click_cate3_price_list_extend = {}\\n                        local new_good_click_seller_id_view_list_extend = {}\\n                        local new_good_click_cate1_view_list_extend = {}\\n                        local new_good_click_cate2_view_list_extend = {}\\n                        local new_good_click_cate3_view_list_extend = {}\\n\\n                        local count = 0\\n                        local len = #good_click_item_id_list\\n                        for index=len,1,-1 do\\n                table.insert(new_good_click_item_id_list_extend, good_click_item_id_list[index])\\n                            table.insert(new_good_click_seller_id_list_extend, good_click_seller_id_list[index])\\n                            table.insert(new_good_click_real_seller_id_list_extend, good_click_real_seller_id_list[index])\\n                            table.insert(new_good_click_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24))\\n                            table.insert(new_good_click_cate1_list_extend, (good_click_category_list[index] >> 48) & 0xffff)\\n                            table.insert(new_good_click_cate2_list_extend, (good_click_category_list[index] >> 32) & 0xffff)\\n                            table.insert(new_good_click_cate3_list_extend, (good_click_category_list[index] >> 16) & 0xffff)\\n                            table.insert(new_good_click_carry_type_list_extend, (good_click_flow_type_list[index]>>16) & 0xff)\\n                            table.insert(new_good_click_click_type_list_extend, (good_click_flow_type_list[index]>>24) & 0xff)\\n                            table.insert(new_good_click_from_list_extend, good_click_from_list[index])\\n                            table.insert(new_good_click_price_list_extend, cal_price(good_click_price_list[index]))\\n                            table.insert(new_good_click_origin_price_list_extend, cal_price(good_click_origin_price_list[index]))\\n                            table.insert(new_good_click_price_diff_list_extend, cal_diff_price(good_click_origin_price_list[index], good_click_price_list[index]))\\n\\n                            table.insert(new_good_click_page_view_time_list_extend, cal_view_time(good_detail_page_view_time_list[index]))\\n                            table.insert(new_good_click_label_list_extend, good_click_label_list[index])\\n                            table.insert(new_good_click_index_list_extend, count)\\n\\n                            table.insert(new_good_click_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600))\\n                            table.insert(new_good_click_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(60))\\n                            \\n                            table.insert(new_good_click_seller_id_lag_list_extend, cal_cross(good_click_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\\n                            table.insert(new_good_click_cate1_lag_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff,\\n                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\\n                            table.insert(new_good_click_cate2_lag_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff,\\n                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\\n                            table.insert(new_good_click_cate3_lag_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff,\\n                                        (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))\\n                            table.insert(new_good_click_seller_id_price_list_extend, cal_cross(good_click_seller_id_list[index], cal_price(good_click_origin_price_list[index])))\\n                            table.insert(new_good_click_cate1_price_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_price(good_click_origin_price_list[index])))\\n                            table.insert(new_good_click_cate2_price_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_price(good_click_origin_price_list[index])))\\n                            table.insert(new_good_click_cate3_price_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_price(good_click_origin_price_list[index])))\\n                            table.insert(new_good_click_seller_id_view_list_extend, cal_cross(good_click_seller_id_list[index], cal_view_time(good_detail_page_view_time_list[index])))\\n                            table.insert(new_good_click_cate1_view_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\\n                            table.insert(new_good_click_cate2_view_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\\n                            table.insert(new_good_click_cate3_view_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))\\n\\n                            count = count + 1\\n                            if count >= 300 then\\n                                break\\n         end\\n            end\\n\\n                        return new_good_click_item_id_list_extend, new_good_click_seller_id_list_extend, new_good_click_real_seller_id_list_extend, new_good_click_lag_list_extend,\\n                        new_good_click_cate1_list_extend, new_good_click_cate2_list_extend,\\n                        new_good_click_cate3_list_extend, new_good_click_carry_type_list_extend, new_good_click_click_type_list_extend,\\n                        new_good_click_from_list_extend, new_good_click_price_list_extend,\\n                        new_good_click_origin_price_list_extend, new_good_click_price_diff_list_extend, new_good_click_page_view_time_list_extend, new_good_click_label_list_extend,\\n                        new_good_click_index_list_extend,\\n                        new_good_click_lag_hour_list_extend, new_good_click_lag_min_list_extend,\\n                        new_good_click_seller_id_lag_list_extend, new_good_click_cate1_lag_list_extend, new_good_click_cate2_lag_list_extend, new_good_click_cate3_lag_list_extend,\\n                        new_good_click_seller_id_price_list_extend, new_good_click_cate1_price_list_extend, new_good_click_cate2_price_list_extend, new_good_click_cate3_price_list_extend,\\n                        new_good_click_seller_id_view_list_extend, new_good_click_cate1_view_list_extend, new_good_click_cate2_view_list_extend, new_good_click_cate3_view_list_extend\\n                    end\",\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'extractmerchantitemcategory1id, extractmerchantitemcategoryid, extractmerchantitemcategory3id, extractmerchantitemcategory2id, extractmerchantitemcategory4id, extractmerchantitemdeliverytime, extractmerchantitemattractivitydiscountprice, extractmerchantitemattractivityprice, extractmerchantitemattrsellerid, extractmerchantitemsales, extractmerchantitemattrprice, extractmerchantitemminprice, extractmerchantitembasicprice, extractphotoproductname'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["x = \"\"\"1. ExtractMerchantItemCategory1Id\n", "2. ExtractMerchantItemCategoryId\n", "3. ExtractMerchantItemCategory3Id\n", "4. ExtractMerchantItemCategory2Id\n", "5. ExtractMerchantItemCategory4Id\n", "6. ExtractMerchantItemDeliveryTime\n", "7. ExtractMerchantItemAttrActivityDiscountPrice\n", "8. ExtractMerchantItemAttrActivityPrice\n", "9. ExtractMerchantItemAttrSellerId\n", "12. ExtractMerchantItemSales\n", "13. ExtractMerchantItemAttrPrice\n", "14. ExtractMerchantItemMinPrice \n", "15. ExtractMerchantItemBasicPrice\n", "16. ExtractPhotoProductName\"\"\".splitlines()\n", "', '.join(i.split('.')[1].strip().lower() for i in x)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "hours = np.arange(0, 1001)\n", "lagV1 = np.floor(np.log(hours / 2 + 1.0) * 10)\n", "lagV1 = np.minimum(lagV1, 200)\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(hours, lagV1, label='lagV1 = min(floor(log(hours/2 + 1)*10), 200)', color='blue')\n", "plt.xlabel('hours_diff')\n", "plt.ylabel('lagV1')\n", "plt.title('lagV1 vs. hours_diff')\n", "plt.grid(True)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}