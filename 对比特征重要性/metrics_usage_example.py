"""
模型评估指标计算器使用示例
展示如何使用 ModelMetricsEvaluator 来分析模型性能
"""

import pandas as pd
from model_metrics_evaluator import ModelMetricsEvaluator


def example_usage():
    """使用示例"""
    
    # 创建评估器实例
    evaluator = ModelMetricsEvaluator()
    
    print("=== 模型评估指标计算器使用示例 ===\n")
    
    # 示例1: 解析单个日志文件
    print("1. 解析日志文件")
    print("   使用方法: evaluator.parse_log_file('log_path')")
    print("   返回: 包含所有评估指标的DataFrame\n")
    
    # 示例2: 解析多个日志文件
    print("2. 解析多个日志文件")
    print("   使用方法: evaluator.parse_log_file(['log1.log', 'log2.log'])")
    print("   返回: 合并后的评估指标DataFrame\n")
    
    # 示例3: 数据过滤
    print("3. 过滤评估数据")
    print("   使用方法: evaluator.filter_metrics_data(df, test_mode=False)")
    print("   参数说明:")
    print("   - test_mode: True(测试模式), False(训练模式), None(不过滤)")
    print("   - exclude_metrics: 要排除的指标列表\n")
    
    # 示例4: 计算差异
    print("4. 计算基线与实验版本的差异")
    print("   使用方法: evaluator.calculate_metrics_diff(base_df, exp_df)")
    print("   返回: 包含差异百分比的DataFrame\n")
    
    # 示例5: 多实验比较
    print("5. 多实验版本比较")
    print("   使用方法: evaluator.compare_experiments(base_df, [exp1_df, exp2_df], metrics)")
    print("   返回: 详细的比较结果DataFrame\n")
    
    print("支持的评估指标:")
    for i, metric in enumerate(evaluator.SUPPORTED_METRICS, 1):
        print(f"   {i:2d}. {metric}")
    
    return evaluator


def complete_workflow_example():
    """完整工作流程示例"""
    
    print("\n=== 完整工作流程示例 ===\n")
    
    evaluator = ModelMetricsEvaluator()
    
    # 模拟的使用流程（实际使用时替换为真实路径）
    workflow_code = '''
# 步骤1: 解析基线和实验日志
base_metrics = evaluator.parse_log_file('/path/to/base.log')
exp_metrics = evaluator.parse_log_file('/path/to/exp.log')

# 步骤2: 过滤数据（只保留训练模式的数据）
base_filtered = evaluator.filter_metrics_data(
    base_metrics, 
    test_mode=False,  # 只看训练数据
    exclude_metrics=['ctl1']  # 排除特定指标
)

exp_filtered = evaluator.filter_metrics_data(
    exp_metrics, 
    test_mode=False
)

# 步骤3: 计算差异
diff_result = evaluator.calculate_metrics_diff(base_filtered, exp_filtered)

# 步骤4: 保存结果
diff_result.to_csv('metrics_comparison.csv')

# 步骤5: 查看关键指标
print("AUC差异:", diff_result['AUC'].mean())
print("UAUC差异:", diff_result['UAUC'].mean())
print("LOSS差异:", diff_result['LOSS'].mean())

# 步骤6: 多实验比较（如果有多个实验版本）
if len(exp_list) > 1:
    comparison_result = evaluator.compare_experiments(
        base_filtered, 
        [exp1_filtered, exp2_filtered], 
        metrics=['metric1', 'metric2']
    )
    comparison_result.to_csv('multi_experiment_comparison.csv')
'''
    
    print("完整使用流程代码:")
    print(workflow_code)


def data_structure_explanation():
    """数据结构说明"""
    
    print("\n=== 数据结构说明 ===\n")
    
    print("1. 解析后的原始数据结构:")
    print("   - 索引: MultiIndex(['test_mode', 'metric'])")
    print("   - test_mode: True(测试模式) / False(训练模式)")
    print("   - metric: 具体的评估指标名称")
    print("   - 列: AUC, UAUC, WUAUC, UserNum, AllUserNum, InsNum, MAE, RMSE, LOSS, ActualCTR, PredictedCTR")
    print()
    
    print("2. 过滤后的数据结构:")
    print("   - 保留指定的test_mode数据")
    print("   - 排除包含'tab'的指标")
    print("   - 移除空值行")
    print()
    
    print("3. 差异计算结果:")
    print("   - 大部分指标: (实验值 - 基线值) * 100 (百分比)")
    print("   - InsNum: 保留基线的原始值")
    print("   - 新增列: pcoc_base, pcoc_exp (预测CTR/实际CTR比值)")
    print()
    
    print("4. 多实验比较结果:")
    print("   - 行索引: base_metric, exp_1_metric, exp_2_metric, ...")
    print("   - 列: 原始指标值 + 对应的_diff列")
    print("   - pcoc列: PredictedCTR/ActualCTR")


def performance_tips():
    """性能优化建议"""
    
    print("\n=== 性能优化建议 ===\n")
    
    tips = [
        "1. 大文件处理: 对于大型日志文件，考虑分批处理或使用生成器",
        "2. 内存优化: 处理完一个文件后及时释放DataFrame内存",
        "3. 并行处理: 多个日志文件可以并行解析",
        "4. 缓存结果: 解析结果可以保存为pickle文件以便重复使用",
        "5. 数据类型优化: 根据实际需要选择合适的数据类型",
        "6. 异常处理: 在生产环境中加强异常处理和日志记录"
    ]
    
    for tip in tips:
        print(f"   {tip}")


def main():
    """主函数"""
    
    # 基本使用示例
    evaluator = example_usage()
    
    # 完整工作流程
    complete_workflow_example()
    
    # 数据结构说明
    data_structure_explanation()
    
    # 性能建议
    performance_tips()
    
    print("\n=== 快速开始 ===")
    print("1. 导入: from model_metrics_evaluator import ModelMetricsEvaluator")
    print("2. 创建: evaluator = ModelMetricsEvaluator()")
    print("3. 解析: metrics = evaluator.parse_log_file('your_log.log')")
    print("4. 过滤: filtered = evaluator.filter_metrics_data(metrics)")
    print("5. 分析: 使用pandas方法进一步分析结果")


if __name__ == "__main__":
    main()
