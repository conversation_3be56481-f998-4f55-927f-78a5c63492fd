import re
import pandas as pd

# 定义全局变量
AUC = 0.0
UE_AUC = 0.0
UE_ROCKET_AUC = 0.0

def parse_log_file_to_dict(log_file_path):
    """
    解析日志文件，返回特征重要性字典
    key: (feature_name, slot), value: importance
    """
    global AUC, UE_AUC, UE_ROCKET_AUC
    feature_data = {}

    try:
        with open(log_file_path, 'r') as file:
            # 读取第一个"after pass run start"之后的内容
            found_start = False
            for line in file:
                if "stdout auc: AUC=" in line:
                    AUC = float(line.split('stdout auc: AUC=')[1].split(' UAUC=')[0])
                elif "stdout ue_auc: AUC=" in line:
                    UE_AUC = float(line.split('stdout ue_auc: AUC=')[1].split(' UAUC=')[0])
                elif "stdout ue_rocket_auc: AUC=" in line:
                    UE_ROCKET_AUC = float(line.split('stdout ue_rocket_auc: AUC=')[1].split(' UAUC=')[0])
                if 'after pass run start' in line:
                    found_start = True
                    break

            if not found_start:
                print(f"在 {log_file_path} 中未找到 'after pass run start' 标记")
                return feature_data

            # 继续读取后续行
            for line in file:
                # 匹配格式: stdout first_stage_ctcvr__sparse16 _slot 625 : 	 7.566834416287312e-07
                match = re.search(r'stdout\s+(.+?)__(\w+)\s+_slot\s+(\d+)\s*:\s+([\d.e-]+)', line)
                if match:
                    feature_name = match.group(2)  # sparse16
                    slot = int(match.group(3))  # 625
                    importance = float(match.group(4))  # 7.566834416287312e-07

                    # 使用(feature_name, slot)作为key，如果重复会自动覆盖
                    key = (feature_name, slot)
                    feature_data[key] = importance

    except FileNotFoundError:
        print(f"文件 {log_file_path} 不存在")
    except Exception as e:
        print(f"解析文件 {log_file_path} 时出错: {e}")

    return feature_data

def compare_importance_files(exp_log_path, base_log_path, output_csv_path, date_label):
    """
    比较exp和base日志文件的特征重要性，导出CSV
    """
    print(f"\n=== 处理 {date_label} 数据 ===")

    # 解析两个日志文件
    exp_data = parse_log_file_to_dict(exp_log_path)
    base_data = parse_log_file_to_dict(base_log_path)

    print(f"EXP文件提取到 {len(exp_data)} 条记录")
    print(f"BASE文件提取到 {len(base_data)} 条记录")

    # 获取所有特征的并集
    all_features = set(exp_data.keys()) | set(base_data.keys())

    # 准备比较数据
    comparison_data = []
    for feature_name, slot in all_features:
        exp_importance = exp_data.get((feature_name, slot), 0.0)
        base_importance = base_data.get((feature_name, slot), 0.0)
        diff = exp_importance - base_importance

        comparison_data.append({
            'feature_name': feature_name,
            'slot': slot,
            'exp_importance': exp_importance,
            'base_importance': base_importance,
            'diff_exp_minus_base': diff
        })

    # 按diff降序排序
    comparison_data.sort(key=lambda x: x['diff_exp_minus_base'], reverse=True)

    # 导出CSV
    df = pd.DataFrame(comparison_data)
    df.to_csv(output_csv_path, index=False, encoding='utf-8')

    print(f"比较结果已导出到: {output_csv_path}")
    print(f"总共比较了 {len(comparison_data)} 个特征")

    # 显示前10条记录
    print(f"\n{date_label} 前10条记录（按diff降序）:")
    for i, record in enumerate(comparison_data[:10], 1):
        print(f"{i}. {record['feature_name']}(slot:{record['slot']}) - "
              f"exp:{record['exp_importance']:.2e}, base:{record['base_importance']:.2e}, "
              f"diff:{record['diff_exp_minus_base']:.2e}")

    return comparison_data

def read_importance_files(exp_log_path, output_csv_path, date_label=''):
    """
    比较exp和base日志文件的特征重要性，导出CSV
    """
    print(f"\n=== 处理 {date_label} 数据 ===")

    # 解析两个日志文件
    exp_data = parse_log_file_to_dict(exp_log_path)

    print(f"EXP文件提取到 {len(exp_data)} 条记录")

    # 获取所有特征的并集
    all_features = set(exp_data.keys())

    # 准备比较数据
    comparison_data = []
    for feature_name, slot in all_features:
        exp_importance = exp_data.get((feature_name, slot), 0.0)

        comparison_data.append({
            'feature_name': feature_name,
            'slot': slot,
            'exp_importance': exp_importance,
        })

    # 按importance降序排序
    comparison_data.sort(key=lambda x: x['exp_importance'], reverse=True)

    # 导出CSV
    df = pd.DataFrame(comparison_data)
    df.to_csv(output_csv_path, index=False, encoding='utf-8')

    print(f"结果已导出到: {output_csv_path}")
    print(f"总共比较了 {len(comparison_data)} 个特征")

    # 显示前10条记录
    print(f"\n{date_label} 前10条记录（按importance降序）:")
    for i, record in enumerate(comparison_data[:10], 1):
        print(f"{i}. {record['feature_name']}(slot:{record['slot']}) - "
              f"exp:{record['exp_importance']:.2e}")

    return comparison_data

def calculate_diff_between_dates(data_0607, data_0613, output_csv_path):
    """
    计算0613 diff - 0607 diff的差值
    """
    print(f"\n=== 计算日期间差值 ===")

    # 将数据转换为字典，便于查找
    dict_0607 = {(row['feature_name'], row['slot']): row['diff_exp_minus_base']
                 for row in data_0607}
    dict_0613 = {(row['feature_name'], row['slot']): row['diff_exp_minus_base']
                 for row in data_0613}

    # 获取所有特征的并集
    all_features = set(dict_0607.keys()) | set(dict_0613.keys())

    # 计算差值
    date_diff_data = []
    for feature_name, slot in all_features:
        diff_0607 = dict_0607.get((feature_name, slot), 0.0)
        diff_0613 = dict_0613.get((feature_name, slot), 0.0)
        date_diff = diff_0613 - diff_0607  # 0613 diff - 0607 diff

        date_diff_data.append({
            'feature_name': feature_name,
            'slot': slot,
            'diff_0607': diff_0607,
            'diff_0613': diff_0613,
            'date_diff_0613_minus_0607': date_diff
        })

    # 按date_diff降序排序
    date_diff_data.sort(key=lambda x: x['date_diff_0613_minus_0607'], reverse=True)

    # 导出CSV
    df = pd.DataFrame(date_diff_data)
    df.to_csv(output_csv_path, index=False, encoding='utf-8')

    print(f"日期间差值结果已导出到: {output_csv_path}")
    print(f"总共计算了 {len(date_diff_data)} 个特征的日期间差值")

    # 显示前10条记录
    print(f"\n前10条记录（按日期间差值降序）:")
    for i, record in enumerate(date_diff_data[:10], 1):
        print(f"{i}. {record['feature_name']}(slot:{record['slot']}) - "
              f"0607_diff:{record['diff_0607']:.2e}, 0613_diff:{record['diff_0613']:.2e}, "
              f"date_diff:{record['date_diff_0613_minus_0607']:.2e}")

    return date_diff_data

def main(logname):
    """
    主函数：执行所有比较任务
    """
    print("开始特征重要性比较分析...")

    # 定义文件路径
    # logname = "xt_test_fix_goods_input-070909"
    log_files = f'/Users/<USER>/Downloads/{logname}.log'

    # 1. 比较0607的exp和base
    data = read_importance_files(
        log_files,
        f'/Users/<USER>/Documents/python/单实验特征重要性/ue_importance_{logname}.csv',
    )

    print("\n=== 重要性分析完成 ===")
    # 打印AUC
    print(f"AUC: {AUC}")
    print(f"UE_AUC: {UE_AUC}")
    print(f"UE_ROCKET_AUC: {UE_ROCKET_AUC}")
    print(f"UE_BOOST: {(UE_ROCKET_AUC - UE_AUC) * 100:.3f} pp")

if __name__ == "__main__":
    # main("cart_order_re_ue_v1-071418")
    # main("dsp_lps_sv_xt_good_click_soft_ue_v2-071418")
    main("sty_ue_re_rocket_071318")
    main("dsp_xt_ue_rocket_add_context_v3-071318")
    main("dsp_xt_ue_rocket_add_context_v4-071318")