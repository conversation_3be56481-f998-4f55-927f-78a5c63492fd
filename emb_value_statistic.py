all_num = []

with open('emb.log','r') as f:
    for i in f.readlines():
        if '[size=64]' in i:
            l = i.split('[size=64]')[-1].strip()
            data = eval('[' +l + ']')
            all_num.extend(data)


print('len, avg, max, min')
print(len(all_num), sum(all_num)/len(all_num), max(all_num), min(all_num))

a = [abs(n) for n in all_num]
print('abs: len, avg, max, min')
print(len(a), sum(a)/len(a), max(a), min(a))

less = []
for n in a:
    if n < 0.00033333 and n > 0:
        less.append(n)
print('len less, less[:10]')
print(len(less), less[:10])
