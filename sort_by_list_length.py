import pandas as pd
import ast
import numpy as np

# Read the CSV file
file_path = '/Users/<USER>/Downloads/2025-07-11_1_Snippet.csv'
df = pd.read_csv(file_path)

# Function to count zeros in the good_click_softsearch_topk_values list
def count_zeros(list_str):
    try:
        if pd.isna(list_str) or list_str == '':
            return 0
        values = ast.literal_eval(list_str)
        return sum(1 for val in values if val == 0)
    except:
        return 0

# Add a column with the count of zeros in good_click_softsearch_topk_values
df['zero_count'] = df['good_click_softsearch_topk_values'].apply(count_zeros)

# Sort by zero count in descending order
sorted_df = df.sort_values(by='zero_count', ascending=False)

# Print (user_id, merchant_product_id) for each row
result = []
for _, row in sorted_df.iterrows():
    if _ < 100 and row['zero_count'] > 0:
        print(f"({row['user_id']}, {row['merchant_product_id']}),")
