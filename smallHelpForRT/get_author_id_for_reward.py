import fire
import pandas as pd
from pathlib import Path

def filter_authors_by_conditions(
    min_submissions: int,
    min_views: int,
    topic_requirement: str,
    tag_requirement: str,
    input_file: pd.DataFrame
):
    """
    根据发奖条件筛选符合条件的作者ID

    Args:
        min_submissions: 最少有效投稿数
        min_views: 最少累计播放量
        topic_requirement: 话题要求
        tag_requirement: tag要求
        input_file: 输入文件路径

    Returns:
        符合条件的作者ID列表
    """
    # 根据条件筛选数据
    # 假设列名为：作者ID, 有效投稿数, 累计播放量, 话题, tag
    # 如果列名不同，需要根据实际情况调整

    def get_tags(tags, tag):
        """检查tag是否在tags字符串中（按逗号分割）"""
        if pd.isna(tags):  # 处理空值
            return False
        return tag in str(tags).split(',')

    df = pd.DataFrame(input_file)

    # 显示数据基本信息
    print(f"原始数据行数: {len(df)}")
    print(f"列名: {list(df.columns)}")

    filtered_df = df[
        (df['话题名称'] == topic_requirement) &
        (df['tag'].apply(lambda tags: get_tags(tags, tag_requirement))) &
        (df['是否话题口径（绑定话题）'] == '是') &
        # (df['是否活动口径（曝光早于投稿）'] == '是') &
        (df['活动期间稿件状态'] == '活动期间保持开放浏览')
    ]

    print(f"筛选后数据行数: {len(filtered_df)}")
    # filtered_df.to_csv(f'filtered_df_{topic_requirement}_views{min_views}.csv', encoding='utf-8-sig')
    # 按照up主id聚合，统计行数和播放次数总和
    grouped_df = filtered_df.groupby('up主id').agg({'稿件id': 'count', '播放次数': 'sum'})
    grouped_df.columns = ['投稿数', '累计播放量']
    grouped_df = grouped_df.sort_values(by=['累计播放量'], ascending=False)
    grouped_df = grouped_df.reset_index()

    # print(f"聚合后数据行数: {len(grouped_df)}")
    # print("聚合数据预览:")
    # print(grouped_df.head())

    # 筛选符合条件的作者ID
    final_df = grouped_df[
        (grouped_df['投稿数'] >= min_submissions) &
        (grouped_df['累计播放量'] >= min_views)
    ]

    print(f"最终符合条件的作者数: {len(final_df)}")

    # 返回只包含作者ID的DataFrame
    result_df = final_df[['up主id']].copy()
    result_df.columns = ['作者ID']  # 重命名列

    return result_df


def save_author_ids_to_excel(result_df: pd.DataFrame, conditions_desc, output_dir="./"):
    """
    将作者ID保存到Excel文件

    Args:
        result_df: 包含作者ID的DataFrame
        conditions_desc: 发奖条件描述
        output_dir: 输出目录

    Returns:
        output_path: 输出文件路径
    """
    # 计算author去重人数
    author_count = len(result_df)
    if author_count == 0:
        print('因 0 人符合该条件，不生成excel表格')
    filename = f"{conditions_desc}_{author_count}人.xlsx"
    output_path = Path(output_dir) / filename

    # 确保作者ID列为文本格式，避免科学计数法
    result_df_copy = result_df.copy()
    result_df_copy['作者ID'] = result_df_copy['作者ID'].astype(str)

    # 保存到Excel文件，不包含表头，确保格式为纯文本
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        result_df_copy.to_excel(
            writer,
            index=False,
            header=False,  # 不包含表头
            sheet_name='Sheet1'
        )

        # 获取工作表对象，设置列格式为文本
        worksheet = writer.sheets['Sheet1']

        # 设置A列（作者ID列）为文本格式
        for row in range(1, len(result_df_copy) + 1):
            cell = worksheet.cell(row=row, column=1)
            cell.number_format = '@'  # '@' 表示文本格式

    # print(f"结果已保存到: {output_path}")
    # print(f"符合条件的作者数量: {author_count}")

    return output_path



def process_single_condition(
    min_submissions: int,
    min_views: int,
    topic_requirement: str,
    tag_requirement: str,
    input_file: str,
    output_dir: str = "./"
):
    """
    处理单个发奖条件
    """
    # 生成条件描述
    conditions_desc = f"投稿{min_submissions}播放{min_views}_{topic_requirement}_{tag_requirement}"

    try:
        # 筛选作者
        result_df = filter_authors_by_conditions(
            min_submissions, min_views, topic_requirement, tag_requirement, input_file
        )

        # 保存结果
        output_path = save_author_ids_to_excel(result_df, conditions_desc, output_dir)

        return {
            'conditions': conditions_desc,
            'author_count': len(result_df),
            'output_file': str(output_path)
        }

    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"处理条件 {conditions_desc} 时出错: {e}")
        return None


def process_all_conditions(
    input_file: str,
    conditions,
    output_dir: str = "./"
):
    # 展开用户目录路径
    input_path = Path(input_file).expanduser()

    if not input_path.exists():
        raise FileNotFoundError(f"输入文件不存在: {input_path}")

    # 读取Excel文件
    print(f"正在读取文件: {input_path}")
    df = pd.read_excel(input_path)

    # 显示数据基本信息
    print(f"原始数据行数: {len(df)}")
    print(f"列名: {list(df.columns)}")

    results = []

    print("开始处理所有发奖条件...")
    print("=" * 50)

    for i, (min_submissions, min_views, topic, tag) in enumerate(conditions, 1):
        print(f"\n处理条件 {i}: 投稿≥{min_submissions}, 播放≥{min_views}, 话题={topic}, tag={tag}")

        result = process_single_condition(
            min_submissions, min_views, topic, tag, df, output_dir
        )

        if result:
            results.append(result)
            print(f"✓ 完成: {result['author_count']}人符合条件")
        else:
            print("✗ 处理失败")

    print("\n" + "=" * 50)
    print("所有条件处理完成!")
    print("\n汇总结果:")
    for result in results:
        print(f"- {result['conditions']}: {result['author_count']}人 -> {result['output_file']}")

    return results


if __name__ == "__main__":
        # 定义所有发奖条件
    conditions = [
        # 条件1: 有效投稿数≥5，累计播放量≥1000，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (5, 1000, "暗区S12冲刺一夏", "暗区突围"),
        # 条件2: 有效投稿数≥10，累计播放量≥2000，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (10, 2000, "暗区S12冲刺一夏", "暗区突围"),
        # 条件3: 有效投稿数≥15，累计播放量≥3000，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (15, 3000, "暗区S12冲刺一夏", "暗区突围"),
        # 条件4: 有效投稿数≥5，累计播放量≥50000，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (5, 50000, "暗区S12冲刺一夏", "暗区突围"),
        # 条件5: 有效投稿数≥10，累计播放量≥100000，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (10, 100000, "暗区S12冲刺一夏", "暗区突围"),
        # 条件6: 有效投稿数≥2，累计播放量≥300，话题要求为暗区S12冲刺一夏，tag为暗区突围
        (2, 300, "暗区S12冲刺一夏", "暗区突围"),
        # 条件7: 有效投稿数≥2，累计播放量≥300，话题要求为暗区大舞台，tag为暗区突围
        (2, 300, "暗区大舞台", "暗区突围"),
    ]
    data_path = "~/Downloads/征稿稿件-暗区S12冲刺一夏-4039094-60016.xlsx"
    process_all_conditions(data_path, conditions)