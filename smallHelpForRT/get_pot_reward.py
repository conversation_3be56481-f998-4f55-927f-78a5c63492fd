from sympy import divisors

def get_factors(target_val, a_min=50, a_max=2500):
    # 获取所有因数
    all_divs = divisors(target_val)
    # 过滤 a 范围
    candidates = [(d, target_val // d) for d in all_divs if a_min <= d <= a_max]
    return candidates

def best_factor_pair(target_val, a_target, b_target, a_min=50, a_max=800):
    # 获取所有因数
    all_divs = divisors(target_val)
    # 过滤 a 范围
    candidates = [(d, target_val // d) for d in all_divs if a_min <= d <= a_max]
    
    if not candidates:
        # 如果没有合法因数，退化：强制 a = a_target 限制在 [a_min, a_max]
        a = min(max(a_target, a_min), a_max)
        b = target_val // a
        return (a, b)
    
    # 选择距离最小的
    best = min(candidates, key=lambda x: abs(x[0] - a_target) + abs(x[1] - b_target))
    return best

s = 0
# 示例数据
for line in """2341 10.67
114 350.87
198 101.01
905 22.09
626 39.93
""".splitlines():
    a, b = line.split()
    a_target = int(a)
    b_target = int(float(b)*100)  # 原始 b 是浮点
    target_val = int(a_target * b_target)
    # pair = best_factor_pair(target_val, a_target, b_target)
    # print(f"目标({a_target},{b_target}) => 选用 {pair}, 乘积={pair[0]*pair[1]}")
    s += a_target * b_target
    print(a,b,s)

# 303 132.01
rest = (18623320 - s)
test_val = rest // 2
for i in range(10000):
    
    x6 = get_factors(test_val + i)
    x7 = get_factors(rest - (test_val + i))
    if x6 and x7:
        print(test_val + i, "valid x6组合:",x6)
        print((rest - (test_val + i)), "valid x7组合:",x7)
        print()
pair = best_factor_pair(target_val, a_target, b_target)
print(f"目标({a_target},{b_target}) => 选用 {pair}, 乘积={pair[0]*pair[1]}")
s += pair[0]*pair[1]
print('sum:',s)


# 2823173 valid x6组合: [(71, 39763), (1207, 2339), (2339, 1207)]
# 15800147 valid x7组合: [(407, 38821)]