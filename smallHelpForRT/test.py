import pandas as pd

def find_unique_values_in_second_column():
    """
    读取Excel文件的第一列和第二列，输出第二列中不在第一列里的值
    """
    # 文件路径
    file_path = "/Users/<USER>/Documents/python/smallHelpForRT/old/投稿2播放300_暗区S12冲刺一夏_暗区突围_1407人.xlsx"

    try:
        # 读取Excel文件，只读取前两列
        df = pd.read_excel(file_path, usecols=[0, 1])

        # 获取第一列和第二列的数据
        first_column = df.iloc[:, 0].dropna()  # 去除空值
        second_column = df.iloc[:, 1].dropna()  # 去除空值

        # 将第一列转换为集合以便快速查找
        first_column_set = set(first_column)

        # 找出第二列中不在第一列里的值
        unique_in_second = []
        for value in second_column:
            if value not in first_column_set:
                unique_in_second.append(value)

        # 去重并返回结果
        unique_values = list(set(unique_in_second))

        print(f"第一列共有 {len(first_column)} 个值")
        print(f"第二列共有 {len(second_column)} 个值")
        print(f"第二列中不在第一列里的值有 {len(unique_values)} 个:")

        for value in unique_values:
            print(f"  - {value}")

        return unique_values

    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []

if __name__ == "__main__":
    # 运行函数
    result = find_unique_values_in_second_column()

