# 作者ID筛选工具使用说明

这是一个用于根据发奖条件筛选符合条件的作者ID的Python工具。本说明专为计算机小白编写，包含详细的安装和使用步骤。

## 📋 功能介绍

这个工具可以：
- 从Excel文件中读取征稿数据
- 根据多个条件筛选符合要求的作者
- 生成包含作者ID的Excel文件（无表头，纯文本格式）
- 支持批量处理多个筛选条件

## 🔧 安装Python环境

### Windows系统

1. **下载Python**
   - 访问 [Python官网](https://www.python.org/downloads/)
   - 点击 "Download Python 3.x.x" 按钮下载最新版本
   - **重要：安装时勾选 "Add Python to PATH"**

2. **验证安装**
   - 按 `Win + R` 键，输入 `cmd`，按回车
   - 在命令行中输入：`python --version`
   - 如果显示版本号，说明安装成功

### macOS系统

1. **使用Homebrew安装（推荐）**
   ```bash
   # 先安装Homebrew（如果没有）
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # 安装Python
   brew install python
   ```

2. **或者从官网下载**
   - 访问 [Python官网](https://www.python.org/downloads/)
   - 下载macOS版本并安装

## 📦 安装依赖包

打开命令行（Windows用cmd，macOS用终端），依次运行以下命令：

```bash
# 安装pandas（用于处理Excel文件）
pip install pandas

# 安装openpyxl（用于读写Excel文件）
pip install openpyxl

# 安装fire（用于命令行参数处理）
pip install fire
```

如果遇到权限问题，可以尝试：
```bash
pip install --user pandas openpyxl fire
```

## 📁 文件准备

1. **下载代码文件**
   - 将 `get_author_id_for_reward.py` 文件保存到你的电脑上
   - 建议创建一个专门的文件夹，比如 `作者筛选工具`

2. **准备数据文件**
   - 将要处理的Excel文件放在合适的位置
   - 记住文件的完整路径

## ⚙️ 修改配置

打开 `get_author_id_for_reward.py` 文件，找到文件末尾的配置部分：

```python
if __name__ == "__main__":
    # 定义所有发奖条件
    conditions = [
        # 条件格式：(最少投稿数, 最少播放量, "话题名称", "tag名称")
        (5, 1000, "暗区S12冲刺一夏", "暗区突围"),
        (10, 2000, "暗区S12冲刺一夏", "暗区突围"),
        # ... 更多条件
    ]
    
    # 修改这里的文件路径为你的Excel文件路径
    data_path = "~/Downloads/征稿稿件-暗区S12冲刺一夏-4039094-60016.xlsx"
    process_all_conditions(data_path, conditions)
```

### 需要修改的地方：

1. **修改数据文件路径**
   ```python
   # 将这行改为你的Excel文件路径
   data_path = "你的文件路径/你的文件名.xlsx"
   
   # 例如：
   # Windows: data_path = "C:/Users/<USER>/Desktop/数据文件.xlsx"
   # macOS: data_path = "/Users/<USER>/Desktop/数据文件.xlsx"
   ```

2. **修改筛选条件**（可选）
   ```python
   conditions = [
       # (最少投稿数, 最少播放量, "话题名称", "tag名称")
       (5, 1000, "你的话题名称", "你的tag名称"),
       (10, 2000, "你的话题名称", "你的tag名称"),
       # 添加更多条件...
   ]
   ```

## 🚀 运行程序

1. **打开命令行**
   - Windows：按 `Win + R`，输入 `cmd`
   - macOS：按 `Cmd + 空格`，输入 `终端`

2. **切换到程序目录**
   ```bash
   # 切换到程序文件所在目录
   cd "你的程序文件夹路径"
   
   # 例如：
   # Windows: cd "C:\Users\<USER>\Desktop\作者筛选工具"
   # macOS: cd "/Users/<USER>/Desktop/作者筛选工具"
   ```

3. **运行程序**
   ```bash
   python get_author_id_for_reward.py
   ```

## 📊 输出文件位置

程序运行完成后，会在**程序文件所在的目录**生成多个Excel文件：

- 文件命名格式：`投稿X播放Y_话题名称_tag名称_Z人.xlsx`
- 例如：`投稿5播放1000_暗区S12冲刺一夏_暗区突围_123人.xlsx`

### 输出文件特点：
- ✅ 无表头（直接是作者ID数据）
- ✅ 纯文本格式（不会出现科学计数法）
- ✅ 每个条件生成一个单独的文件
- ✅ 文件名包含符合条件的人数

## 🔍 程序运行示例

运行时会看到类似这样的输出：
```
正在读取文件: /Users/<USER>/Downloads/征稿稿件.xlsx
原始数据行数: 5000
列名: ['up主id', '稿件id', '话题名称', 'tag', '播放次数', ...]

开始处理所有发奖条件...
==================================================

处理条件 1: 投稿≥5, 播放≥1000, 话题=暗区S12冲刺一夏, tag=暗区突围
筛选后数据行数: 1200
最终符合条件的作者数: 123
✓ 完成: 123人符合条件

处理条件 2: 投稿≥10, 播放≥2000, 话题=暗区S12冲刺一夏, tag=暗区突围
筛选后数据行数: 800
最终符合条件的作者数: 56
✓ 完成: 56人符合条件

==================================================
所有条件处理完成!

汇总结果:
- 投稿5播放1000_暗区S12冲刺一夏_暗区突围: 123人 -> ./投稿5播放1000_暗区S12冲刺一夏_暗区突围_123人.xlsx
- 投稿10播放2000_暗区S12冲刺一夏_暗区突围: 56人 -> ./投稿10播放2000_暗区S12冲刺一夏_暗区突围_56人.xlsx
```

## ❗ 常见问题

### 1. 提示"找不到模块"
```bash
# 重新安装依赖包
pip install pandas openpyxl fire
```

### 2. 提示"文件不存在"
- 检查Excel文件路径是否正确
- 确保文件名和路径中没有特殊字符
- 使用绝对路径而不是相对路径

### 3. 程序运行没有输出文件
- 检查是否有符合条件的数据
- 查看程序输出的错误信息
- 确认Excel文件的列名是否与程序中的列名匹配

### 4. Excel文件打不开或格式错误
- 确保安装了openpyxl：`pip install openpyxl`
- 检查原始Excel文件是否损坏

## 📞 技术支持

如果遇到问题：
1. 仔细阅读错误信息
2. 检查文件路径和配置是否正确
3. 确认所有依赖包都已安装
4. 联系技术人员获取帮助

---

**注意：** 请确保你的Excel数据文件包含以下必要的列：
- `up主id`
- `稿件id`
- `话题名称`
- `tag`
- `播放次数`
- `是否话题口径（绑定话题）`
- `是否活动口径（曝光早于投稿）`
- `活动期间稿件状态`
