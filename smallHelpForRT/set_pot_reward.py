import math

def closest_product_with_a_range(b, a_min=50, a_max=800):
    """找 <= b 的最大乘积，要求 a 在 [a_min, a_max]"""
    best = 0
    best_pair = (a_min, 1)
    for a in range(min(a_max, b), a_min - 1, -1):  # 从大到小尝试
        q = b // a
        if q < 1:
            continue
        val = a * q
        if val <= b and val > best:
            best = val
            best_pair = (a, q)
            if best == b:  # 完全匹配提前退出
                break
    return best, best_pair

def adjust_to_product_with_a_range(n, a_min=50, a_max=800):
    """让 n 变成乘积，a 在范围内"""
    if n <= 0:
        return 0, (a_min, 1)
    best = 0
    best_pair = (a_min, 1)
    for a in range(min(a_max, n), a_min - 1, -1):
        q = n // a
        if q < 1:
            continue
        val = a * q
        if val > best:
            best = val
            best_pair = (a, q)
    return best, best_pair

def split_with_bounds_a_range(N, bounds, a_min=50, a_max=800):
    x = []
    pairs = []
    for b in bounds:
        val, pair = closest_product_with_a_range(b, a_min, a_max)
        x.append(val)
        pairs.append(pair)
    sum6 = sum(x)
    remain = N - sum6
    
    # 如果 remain <= 0，必须回退调整
    if remain <= 0:
        diff = -remain
        for i in reversed(range(6)):
            if diff == 0:
                break
            reducible = x[i] - a_min  # 保证至少 a_min
            take = min(diff, reducible)
            x[i] -= take
            diff -= take
        remain = N - sum(x)
    
    # 保证最后一个也符合 a 范围
    if remain < a_min:
        raise ValueError("N 太小，无法满足约束")
    val7, pair7 = adjust_to_product_with_a_range(remain, a_min, a_max)
    x.append(val7)
    pairs.append(pair7)
    return x, pairs

# 示例测试
# N = 1000
# bounds = [150, 140, 135, 145, 138, 142]
N = 18623320
bounds = [i*100000 for i in [25,40,20,40,20,25]]
x, pairs = split_with_bounds_a_range(N, bounds)
print("结果:", x, "和:", sum(x))
print("因数对:", pairs)