{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["按顺序输入需要实际做哈希，然后训练可能用到的特征和对应的slot起始点，以及需要的size  创建对应map"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["add_feature = ['good_click_item_id_list_top100',\n", "            'good_click_seller_id_list_top100',\n", "            'good_click_real_seller_id_list_top100',\n", "            'good_click_lag_list_top100',\n", "            'good_click_cate1_list_top100',\n", "            'good_click_cate2_list_top100',\n", "            'good_click_cate3_list_top100',\n", "            'good_click_category_list_top100',\n", "            'good_click_carry_type_list_top100',\n", "            'good_click_click_type_list_top100',\n", "            'good_click_from_list_top100',\n", "            'good_click_price_list_top100',\n", "            'good_click_page_view_time_list_top100',\n", "            'good_click_label_list_top100',\n", "            'good_click_item_count_list_top100',\n", "            'good_click_lag_hour_list_top100',\n", "            'good_click_lag_min_list_top100',]\n", "\n", "first_slot = 840\n", "size_map = {\n", "    'good_click_item_id_list_top100': 100_0001, # share good_click_cate2cate_item_id_list_extend\n", "    'good_click_seller_id_list_top100': 300_0001, # share good_click_cate2cate_seller_id_list_extend\n", "    'good_click_real_seller_id_list_top100': 1_0000_0001, # 暂时不上\n", "    'good_click_lag_list_top100': 2001, # share good_click_cate2cate_lag_list_extend\n", "    'good_click_cate1_list_top100': 100, # hashsize变动\n", "    'good_click_cate2_list_top100': 10001, # hashsize变动\n", "    'good_click_cate3_list_top100': 10001, # hashsize变动\n", "    'good_click_category_list_top100': 10_0001, # share good_click_cate2cate_category_list_extend\n", "    'good_click_carry_type_list_top100':10, # share good_click_cate2cate_carry_type_list_extend\n", "    'good_click_click_type_list_top100':11,\n", "    'good_click_from_list_top100':100_0001,\n", "    'good_click_price_list_top100':310, # share good_click_cate2cate_real_price_list_extend\n", "    'good_click_page_view_time_list_top100':301, # cal逻辑变动\n", "    'good_click_label_list_top100':101,\n", "    # 'good_click_uniform_spu_id_list_top100':1000_0001, # 可以share colossus_rs_uniform_spu_id_list\n", "    'good_click_item_count_list_top100':2001,\n", "    'good_click_lag_hour_list_top100':10_0001,\n", "    'good_click_lag_min_list_top100':1_0000_0001,\n", "}\n", "prefix = \"good_click_\"\n", "kuiba_prefix = \"good_click_top100_\"\n", "final_prefix = \"good_click_softsearch_\""]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["name2slot_map = {name: first_slot + i for i, name in enumerate(add_feature)}\n", "slot2name_map = {v: k for k, v in name2slot_map.items()}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建kuiba config"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('good_click_item_id_list_top100', 840, 'good_click_item_id_list_top100'),\n", " ('good_click_seller_id_list_top100', 841, 'good_click_seller_id_list_top100'),\n", " ('good_click_real_seller_id_list_top100',\n", "  842,\n", "  'good_click_real_seller_id_list_top100'),\n", " ('good_click_lag_list_top100', 843, 'good_click_lag_list_top100'),\n", " ('good_click_cate1_list_top100', 844, 'good_click_cate1_list_top100'),\n", " ('good_click_cate2_list_top100', 845, 'good_click_cate2_list_top100'),\n", " ('good_click_cate3_list_top100', 846, 'good_click_cate3_list_top100'),\n", " ('good_click_category_list_top100', 847, 'good_click_category_list_top100'),\n", " ('good_click_carry_type_list_top100',\n", "  848,\n", "  'good_click_carry_type_list_top100'),\n", " ('good_click_click_type_list_top100',\n", "  849,\n", "  'good_click_click_type_list_top100'),\n", " ('good_click_from_list_top100', 850, 'good_click_from_list_top100'),\n", " ('good_click_price_list_top100', 851, 'good_click_price_list_top100'),\n", " ('good_click_page_view_time_list_top100',\n", "  852,\n", "  'good_click_page_view_time_list_top100'),\n", " ('good_click_label_list_top100', 853, 'good_click_label_list_top100'),\n", " ('good_click_item_count_list_top100',\n", "  854,\n", "  'good_click_item_count_list_top100'),\n", " ('good_click_lag_hour_list_top100', 855, 'good_click_lag_hour_list_top100'),\n", " ('good_click_lag_min_list_top100', 856, 'good_click_lag_min_list_top100')]"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["kuiba_param_config = [(k,v,k) for k,v in name2slot_map.items()]\n", "kuiba_param_config"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据featuresize, 创建item map"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["slot_as_attr_name_prefix=\"good_click_top100_\",\n", "item_map_list = \n"]}, {"data": {"text/plain": ["[['840', 840, 840, 1000001],\n", " ['841', 841, 841, 3000001],\n", " ['842', 842, 842, 100000001],\n", " ['843', 843, 843, 2001],\n", " ['844', 844, 844, 100],\n", " ['845', 845, 845, 10001],\n", " ['846', 846, 846, 10001],\n", " ['847', 847, 847, 100001],\n", " ['848', 848, 848, 10],\n", " ['849', 849, 849, 11],\n", " ['850', 850, 850, 1000001],\n", " ['851', 851, 851, 310],\n", " ['852', 852, 852, 301],\n", " ['853', 853, 853, 101],\n", " ['854', 854, 854, 2001],\n", " ['855', 855, 855, 100001],\n", " ['856', 856, 856, 100000001]]"]}, "execution_count": 163, "metadata": {}, "output_type": "execute_result"}], "source": ["item_map = [[str(slot), slot, slot, size_map[name]] for name,slot in name2slot_map.items()]\n", "\n", "print(f'slot_as_attr_name_prefix=\"{kuiba_prefix}\",')\n", "print('item_map_list = ')\n", "item_map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建copy attr参数"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'from_item': 'good_click_top100_840',\n", "  'to_item': 'good_click_softsearch_item_id_list_top100'},\n", " {'from_item': 'good_click_top100_841',\n", "  'to_item': 'good_click_softsearch_seller_id_list_top100'},\n", " {'from_item': 'good_click_top100_842',\n", "  'to_item': 'good_click_softsearch_real_seller_id_list_top100'},\n", " {'from_item': 'good_click_top100_843',\n", "  'to_item': 'good_click_softsearch_lag_list_top100'},\n", " {'from_item': 'good_click_top100_844',\n", "  'to_item': 'good_click_softsearch_cate1_list_top100'},\n", " {'from_item': 'good_click_top100_845',\n", "  'to_item': 'good_click_softsearch_cate2_list_top100'},\n", " {'from_item': 'good_click_top100_846',\n", "  'to_item': 'good_click_softsearch_cate3_list_top100'},\n", " {'from_item': 'good_click_top100_847',\n", "  'to_item': 'good_click_softsearch_category_list_top100'},\n", " {'from_item': 'good_click_top100_848',\n", "  'to_item': 'good_click_softsearch_carry_type_list_top100'},\n", " {'from_item': 'good_click_top100_849',\n", "  'to_item': 'good_click_softsearch_click_type_list_top100'},\n", " {'from_item': 'good_click_top100_850',\n", "  'to_item': 'good_click_softsearch_from_list_top100'},\n", " {'from_item': 'good_click_top100_851',\n", "  'to_item': 'good_click_softsearch_price_list_top100'},\n", " {'from_item': 'good_click_top100_852',\n", "  'to_item': 'good_click_softsearch_page_view_time_list_top100'},\n", " {'from_item': 'good_click_top100_853',\n", "  'to_item': 'good_click_softsearch_label_list_top100'},\n", " {'from_item': 'good_click_top100_854',\n", "  'to_item': 'good_click_softsearch_item_count_list_top100'},\n", " {'from_item': 'good_click_top100_855',\n", "  'to_item': 'good_click_softsearch_lag_hour_list_top100'},\n", " {'from_item': 'good_click_top100_856',\n", "  'to_item': 'good_click_softsearch_lag_min_list_top100'}]"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["copy_attr = [{\"from_item\": f\"{kuiba_prefix}{slot}\", \"to_item\": f\"{name.replace(prefix, final_prefix)}\"} for name,slot in name2slot_map.items()]\n", "copy_attr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# fg_schema_out"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["column_name=good_click_softsearch_item_id_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_seller_id_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_real_seller_id_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_lag_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate1_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate2_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_cate3_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_category_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_carry_type_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_click_type_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_from_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_price_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_page_view_time_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_label_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_item_count_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_lag_hour_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_lag_min_list_top100, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_topk_indices, compression_type=None, column_type=List<Long>\n", "column_name=good_click_softsearch_topk_values, compression_type=None, column_type=List<Long>\n"]}, {"data": {"text/plain": ["19"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["final_name = [name.replace(prefix, final_prefix) for name,slot in name2slot_map.items()]\n", "final_name.extend([\"good_click_softsearch_topk_indices\",\"good_click_softsearch_topk_values\"])\n", "\n", "\n", "for i in final_name:\n", "    print(f\"column_name={i}, compression_type=None, column_type=List<Long>\")\n", "\n", "len(final_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 样本流sql"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_list_top100 array<bigint>,\n", "good_click_softsearch_seller_id_list_top100 array<bigint>,\n", "good_click_softsearch_real_seller_id_list_top100 array<bigint>,\n", "good_click_softsearch_lag_list_top100 array<bigint>,\n", "good_click_softsearch_cate1_list_top100 array<bigint>,\n", "good_click_softsearch_cate2_list_top100 array<bigint>,\n", "good_click_softsearch_cate3_list_top100 array<bigint>,\n", "good_click_softsearch_category_list_top100 array<bigint>,\n", "good_click_softsearch_carry_type_list_top100 array<bigint>,\n", "good_click_softsearch_click_type_list_top100 array<bigint>,\n", "good_click_softsearch_from_list_top100 array<bigint>,\n", "good_click_softsearch_price_list_top100 array<bigint>,\n", "good_click_softsearch_page_view_time_list_top100 array<bigint>,\n", "good_click_softsearch_label_list_top100 array<bigint>,\n", "good_click_softsearch_item_count_list_top100 array<bigint>,\n", "good_click_softsearch_lag_hour_list_top100 array<bigint>,\n", "good_click_softsearch_lag_min_list_top100 array<bigint>,\n", "good_click_softsearch_topk_indices array<bigint>,\n", "good_click_softsearch_topk_values array<bigint>,\n"]}], "source": ["for i in final_name:\n", "    print(f\"{i} array<bigint>,\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型训练 文件"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["finalname2size_map = {}\n", "for name,slot in name2slot_map.items():\n", "    finalname2size_map[name.replace(prefix, final_prefix)] = size_map[name]\n", "\n", "finalname2size_map[\"good_click_softsearch_topk_indices\"] = 101\n", "finalname2size_map[\"good_click_softsearch_topk_values\"] = 101"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## kai feature\n", "目前仅考虑稀疏特征\n", "\n", "注意看category user/photo/combine，写错不影响离线训练，影响上线\n", "\n", "dragon序列需要去掉slot size=0, no_default=1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["class=good_click_softsearch_item_id_list_top100, category=ad, field=0, slot=840, size=1000001, topic_id=0\n", "class=good_click_softsearch_seller_id_list_top100, category=ad, field=0, slot=841, size=3000001, topic_id=0\n", "class=good_click_softsearch_lag_list_top100, category=ad, field=0, slot=842, size=2001, topic_id=0\n", "class=good_click_softsearch_category_list_top100, category=ad, field=0, slot=843, size=100001, topic_id=0\n", "class=good_click_softsearch_carry_type_list_top100, category=ad, field=0, slot=844, size=10, topic_id=0\n", "class=good_click_softsearch_price_list_top100, category=ad, field=0, slot=845, size=310, topic_id=0\n", "class=good_click_softsearch_item_count_list_top100, category=ad, field=0, slot=846, size=2001, topic_id=0\n", "class=good_click_softsearch_topk_values, category=ad, field=0, slot=847, size=101, topic_id=0\n", "class=good_click_softsearch_topk_indices, category=ad, field=0, slot=848, size=101, topic_id=0\n"]}], "source": ["target = \"good_click_softsearch_item_id_list_top100,good_click_softsearch_seller_id_list_top100,good_click_softsearch_lag_list_top100,good_click_softsearch_category_list_top100,good_click_softsearch_carry_type_list_top100,good_click_softsearch_price_list_top100,good_click_softsearch_item_count_list_top100,good_click_softsearch_topk_values,good_click_softsearch_topk_indices\".split(',')\n", "start_slot = 840\n", "category = \"combine\" # user/photo/combine\n", "\n", "slot = start_slot\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    print(f\"class={i}, category={category}, field=0, size=0, no_default=1, topic_id=0\")\n", "    slot += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## feature remap\n", "填写dim，没出现的默认dim=4\n", "\n", "结果记得**手动改map_slot**，然后field_adjust.py"]}, {"cell_type": "code", "execution_count": 178, "metadata": {}, "outputs": [{"data": {"text/plain": ["({'good_click_softsearch_item_id_list_top100': 16,\n", "  'good_click_softsearch_seller_id_list_top100': 16,\n", "  'good_click_softsearch_lag_list_top100': 8,\n", "  'good_click_softsearch_category_list_top100': 8,\n", "  'good_click_softsearch_carry_type_list_top100': 4,\n", "  'good_click_softsearch_price_list_top100': 8,\n", "  'good_click_softsearch_item_count_list_top100': 4,\n", "  'good_click_softsearch_topk_values': 4,\n", "  'good_click_softsearch_topk_indices': 4},\n", " 72)"]}, "execution_count": 178, "metadata": {}, "output_type": "execute_result"}], "source": ["all_dims = [16,16,8,8,4,8,4,4,4]\n", "column2dim = {k:all_dims[idx] for idx,k in enumerate(target)}\n", "column2dim, sum(all_dims)"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["remap_slot=840, dim=16, map_slot=840, size=0, slot=0, name=good_click_softsearch_item_id_list_top100, category=ad, field=0, feature_size=1000001, topic_id=0\n", "remap_slot=841, dim=16, map_slot=841, size=0, slot=0, name=good_click_softsearch_seller_id_list_top100, category=ad, field=0, feature_size=3000001, topic_id=0\n", "remap_slot=842, dim=8, map_slot=842, size=0, slot=0, name=good_click_softsearch_lag_list_top100, category=ad, field=0, feature_size=2001, topic_id=0\n", "remap_slot=843, dim=8, map_slot=843, size=0, slot=0, name=good_click_softsearch_category_list_top100, category=ad, field=0, feature_size=100001, topic_id=0\n", "remap_slot=844, dim=4, map_slot=844, size=0, slot=0, name=good_click_softsearch_carry_type_list_top100, category=ad, field=0, feature_size=10, topic_id=0\n", "remap_slot=845, dim=8, map_slot=845, size=0, slot=0, name=good_click_softsearch_price_list_top100, category=ad, field=0, feature_size=310, topic_id=0\n", "remap_slot=846, dim=4, map_slot=846, size=0, slot=0, name=good_click_softsearch_item_count_list_top100, category=ad, field=0, feature_size=2001, topic_id=0\n", "remap_slot=847, dim=4, map_slot=847, size=0, slot=0, name=good_click_softsearch_topk_values, category=ad, field=0, feature_size=101, topic_id=0\n", "remap_slot=848, dim=4, map_slot=848, size=0, slot=0, name=good_click_softsearch_topk_indices, category=ad, field=0, feature_size=101, topic_id=0\n"]}], "source": ["slot = start_slot\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    dim = column2dim.get(i, 4)\n", "    print(f\"remap_slot={slot}, dim={dim}, map_slot={slot}, size=0, slot=0, name={i}, category=ad, field=0, feature_size={size}, topic_id=0\")\n", "    slot += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## kai_v2_model.py\n", "输入adjust之后的第一个field"]}, {"cell_type": "code", "execution_count": 171, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_list_top100 = kai.new_embedding('good_click_softsearch_item_id_list_top100', dim=16, slots=[840], expand=50)\n", "good_click_softsearch_seller_id_list_top100 = kai.new_embedding('good_click_softsearch_seller_id_list_top100', dim=16, slots=[841], expand=50)\n", "good_click_softsearch_lag_list_top100 = kai.new_embedding('good_click_softsearch_lag_list_top100', dim=8, slots=[842], expand=50)\n", "good_click_softsearch_category_list_top100 = kai.new_embedding('good_click_softsearch_category_list_top100', dim=8, slots=[843], expand=50)\n", "good_click_softsearch_carry_type_list_top100 = kai.new_embedding('good_click_softsearch_carry_type_list_top100', dim=4, slots=[844], expand=50)\n", "good_click_softsearch_price_list_top100 = kai.new_embedding('good_click_softsearch_price_list_top100', dim=8, slots=[845], expand=50)\n", "good_click_softsearch_item_count_list_top100 = kai.new_embedding('good_click_softsearch_item_count_list_top100', dim=4, slots=[846], expand=50)\n", "good_click_softsearch_topk_values = kai.new_embedding('good_click_softsearch_topk_values', dim=4, slots=[847], expand=50)\n", "good_click_softsearch_topk_indices = kai.new_embedding('good_click_softsearch_topk_indices', dim=4, slots=[848], expand=50)\n"]}], "source": ["# 输入adjust之后的第一个field\n", "start_field = 473\n", "seq_len = 50\n", "\n", "slot = start_slot\n", "for i in target:\n", "    size = finalname2size_map.get(i)\n", "    dim = column2dim.get(i, 4)\n", "    # 为了避免mapslot和remapslot重复，需要添加offset\n", "    remap_slot = slot\n", "    map_slot = slot\n", "    kai2_emb = f\"{i} = kai.new_embedding('{i}', dim={dim}, slots=[{remap_slot}], expand={seq_len})\"\n", "    print(kai2_emb)\n", "    start_field += 1\n", "    slot += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来是dnn input"]}, {"cell_type": "code", "execution_count": 172, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_list_top100,good_click_softsearch_seller_id_list_top100,good_click_softsearch_lag_list_top100,good_click_softsearch_category_list_top100,good_click_softsearch_carry_type_list_top100,good_click_softsearch_price_list_top100,good_click_softsearch_item_count_list_top100,good_click_softsearch_topk_values,good_click_softsearch_topk_indices\n"]}], "source": ["\n", "#### 接下来是dnn input\n", "all_cols = ','.join(target)\n", "print(all_cols)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来是model_def"]}, {"cell_type": "code", "execution_count": 173, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_list_top100 = block_data[83].output\n", "good_click_softsearch_seller_id_list_top100 = block_data[84].output\n", "good_click_softsearch_lag_list_top100 = block_data[85].output\n", "good_click_softsearch_category_list_top100 = block_data[86].output\n", "good_click_softsearch_carry_type_list_top100 = block_data[87].output\n", "good_click_softsearch_price_list_top100 = block_data[88].output\n", "good_click_softsearch_item_count_list_top100 = block_data[89].output\n", "good_click_softsearch_topk_values = block_data[90].output\n", "good_click_softsearch_topk_indices = block_data[91].output\n", "good_click_softsearch_user_embedding = tf.concat([good_click_softsearch_item_id_list_top100,good_click_softsearch_seller_id_list_top100,good_click_softsearch_lag_list_top100,good_click_softsearch_category_list_top100,good_click_softsearch_carry_type_list_top100,good_click_softsearch_price_list_top100,good_click_softsearch_item_count_list_top100,good_click_softsearch_topk_values,good_click_softsearch_topk_indices], 1)\n", "\n", "good_click_softsearch_user_embedding = tf.contrib.layers.layer_norm(good_click_softsearch_user_embedding, scope=\"good_click_softsearch_user_embedding\")\n"]}], "source": ["#### 接下来是model_def\n", "origin_dnn_input_string = \"\"\"sparse0, sparse1, sparse2, sparse3, sparse4, sparse5, sparse6, sparse7,sparse8, sparse9, sparse10,sparse11,sparse12, sparse13, sparse14, sparse15, SIM_G_SEQ_16_380, SIM_G_SEQ_16_381, SIM_G_SEQ_8_382, SIM_G_SEQ_8_383, SIM_G_SEQ_8_384, akg_combine_371, ExtractPhotoSharkEmbedding, ExtractItemSharkEmbedding, ExtractUserPinnerformerEmbedding, ExtractItemPinnerformerEmbedding, dense, cot_user, cot_photo, cot_dense, entity, is_ai_tag, ExtractUserDenseAdClick7dVideolClipEmb, ExtractUserDenseAdClick30dVideolClipEmb, ExtractUserDenseAdClick60dVideolClipEmb, ExtractUserDenseOrderpay7dVideolClipEmb, ExtractUserDenseOrderpay30dVideolClipEmb, ExtractUserDenseOrderpay60dVideolClipEmb, ExtractPhotoConcatKeywordVideoClip, reco_sparse, RECO_SIM_G_SEQ_16_380, RECO_SIM_G_SEQ_16_381, RECO_SIM_G_SEQ_8_382, RECO_SIM_G_SEQ_8_383, RECO_SIM_G_SEQ_8_384, reco_dense, sft_dense, reco_sft_dense, multimodal_emb, coupon, match_dense, user_ecom_rq, ecom_multimodal_emb, ec_detail, ExtractDensePhotoCommentStats, ExtractPhotoQcpxCouponAmt, dpo, ExtractUserDenseDpoFea,\n", "            good_click_cate2cate_real_price_list_extend, good_click_cate2cate_category_list_extend, good_click_cate2cate_carry_type_list_extend, good_click_cate2cate_lag_list_extend, good_click_cate2cate_item_id_list_extend, good_click_cate2cate_seller_id_list_extend,\n", "            colossus_rs_count_index_list,colossus_rs_item_id_list,colossus_rs_lagV1_list,colossus_rs_lagV2_list,colossus_rs_pagecode_id_list,colossus_rs_uniform_spu_id_list,\n", "            eshop_ad,\n", "            good_click_cate2cate_cate1_list_extend, good_click_cate2cate_lag_hour_list_extend, good_click_cate2cate_click_type_list_extend, good_click_cate2cate_index_list_extend, \n", "            cart_photo_exposure_pid_list_expand, cart_photo_exposure_aid_list_expand, cart_photo_exposure_duration_list_expand, cart_photo_exposure_play_time_list_expand, cart_photo_exposure_channel_list_expand, cart_photo_exposure_spu_id_list_expand, cart_photo_exposure_category_list_expand,\n", "            product_id\"\"\".split(',')\n", "block_start = len(origin_dnn_input_string)\n", "# print('origin dnn input len: ', block_start)\n", "for col in target:\n", "    print(f'{col} = block_data[{block_start}].output')\n", "    block_start += 1\n", "\n", "\n", "print(f'good_click_softsearch_user_embedding = tf.concat([{all_cols}], 1)')\n", "print()\n", "print(f'good_click_softsearch_user_embedding = tf.contrib.layers.layer_norm(good_click_softsearch_user_embedding, scope=\"good_click_softsearch_user_embedding\")')\n"]}, {"cell_type": "code", "execution_count": 174, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 0], [-1, 800])\n", "good_click_softsearch_seller_id_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 800], [-1, 800])\n", "good_click_softsearch_lag_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 1600], [-1, 400])\n", "good_click_softsearch_category_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 2000], [-1, 400])\n", "good_click_softsearch_carry_type_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 2400], [-1, 200])\n", "good_click_softsearch_price_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 2600], [-1, 400])\n", "good_click_softsearch_item_count_list_top100 = tf.slice(good_click_softsearch_user_embedding, [0, 3000], [-1, 200])\n", "good_click_softsearch_topk_values = tf.slice(good_click_softsearch_user_embedding, [0, 3200], [-1, 200])\n", "good_click_softsearch_topk_indices = tf.slice(good_click_softsearch_user_embedding, [0, 3400], [-1, 200])\n", "\n", "good_click_softsearch_item_id_list_top100 = tf.reshape(good_click_softsearch_item_id_list_top100, [-1, 50, 16])\n", "good_click_softsearch_seller_id_list_top100 = tf.reshape(good_click_softsearch_seller_id_list_top100, [-1, 50, 16])\n", "good_click_softsearch_lag_list_top100 = tf.reshape(good_click_softsearch_lag_list_top100, [-1, 50, 8])\n", "good_click_softsearch_category_list_top100 = tf.reshape(good_click_softsearch_category_list_top100, [-1, 50, 8])\n", "good_click_softsearch_carry_type_list_top100 = tf.reshape(good_click_softsearch_carry_type_list_top100, [-1, 50, 4])\n", "good_click_softsearch_price_list_top100 = tf.reshape(good_click_softsearch_price_list_top100, [-1, 50, 8])\n", "good_click_softsearch_item_count_list_top100 = tf.reshape(good_click_softsearch_item_count_list_top100, [-1, 50, 4])\n", "good_click_softsearch_topk_values = tf.reshape(good_click_softsearch_topk_values, [-1, 50, 4])\n", "good_click_softsearch_topk_indices = tf.reshape(good_click_softsearch_topk_indices, [-1, 50, 4])\n"]}], "source": ["# good_click_softsearch_user_embedding是沿着dim*seq维度拼接的，再slice和reshape为各条特征\n", "start_dim = 0\n", "for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{col} = tf.slice(good_click_softsearch_user_embedding, [0, {start_dim}], [-1, {dim * seq_len}])')\n", "    start_dim += dim * seq_len\n", "    block_start += 1\n", "print()\n", "for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{col} = tf.reshape({col}, [-1, {seq_len}, {dim}])')\n", "    start_dim += dim\n", "    block_start += 1\n"]}, {"cell_type": "code", "execution_count": 175, "metadata": {}, "outputs": [], "source": ["def change_suffix(s, a, b):\n", "    return s.<PERSON><PERSON><PERSON>(a) + b"]}, {"cell_type": "code", "execution_count": 176, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_item_id_fake_seq1, good_click_softsearch_item_id_fake_seq2 = self.mask_and_dropout(good_click_softsearch_item_id_list_top100, 50, 16)\n", "good_click_softsearch_seller_id_fake_seq1, good_click_softsearch_seller_id_fake_seq2 = self.mask_and_dropout(good_click_softsearch_seller_id_list_top100, 50, 16)\n", "good_click_softsearch_lag_fake_seq1, good_click_softsearch_lag_fake_seq2 = self.mask_and_dropout(good_click_softsearch_lag_list_top100, 50, 8)\n", "good_click_softsearch_category_fake_seq1, good_click_softsearch_category_fake_seq2 = self.mask_and_dropout(good_click_softsearch_category_list_top100, 50, 8)\n", "good_click_softsearch_carry_type_fake_seq1, good_click_softsearch_carry_type_fake_seq2 = self.mask_and_dropout(good_click_softsearch_carry_type_list_top100, 50, 4)\n", "good_click_softsearch_price_fake_seq1, good_click_softsearch_price_fake_seq2 = self.mask_and_dropout(good_click_softsearch_price_list_top100, 50, 8)\n", "good_click_softsearch_item_count_fake_seq1, good_click_softsearch_item_count_fake_seq2 = self.mask_and_dropout(good_click_softsearch_item_count_list_top100, 50, 4)\n", "good_click_softsearch_topk_values_fake_seq1, good_click_softsearch_topk_values_fake_seq2 = self.mask_and_dropout(good_click_softsearch_topk_values, 50, 4)\n", "good_click_softsearch_topk_indices_fake_seq1, good_click_softsearch_topk_indices_fake_seq2 = self.mask_and_dropout(good_click_softsearch_topk_indices, 50, 4)\n", "\n", "good_click_softsearch_item_id_fake_emb1 = self.ssl_tower(good_click_softsearch_item_id_fake_seq1, None, \"good_click_softsearch_item_id_fake\")\n", "good_click_softsearch_item_id_fake_emb2 = self.ssl_tower(good_click_softsearch_item_id_fake_seq2, None, \"good_click_softsearch_item_id_fake\")\n", "good_click_softsearch_seller_id_fake_emb1 = self.ssl_tower(good_click_softsearch_seller_id_fake_seq1, None, \"good_click_softsearch_seller_id_fake\")\n", "good_click_softsearch_seller_id_fake_emb2 = self.ssl_tower(good_click_softsearch_seller_id_fake_seq2, None, \"good_click_softsearch_seller_id_fake\")\n", "good_click_softsearch_lag_fake_emb1 = self.ssl_tower(good_click_softsearch_lag_fake_seq1, None, \"good_click_softsearch_lag_fake\")\n", "good_click_softsearch_lag_fake_emb2 = self.ssl_tower(good_click_softsearch_lag_fake_seq2, None, \"good_click_softsearch_lag_fake\")\n", "good_click_softsearch_category_fake_emb1 = self.ssl_tower(good_click_softsearch_category_fake_seq1, None, \"good_click_softsearch_category_fake\")\n", "good_click_softsearch_category_fake_emb2 = self.ssl_tower(good_click_softsearch_category_fake_seq2, None, \"good_click_softsearch_category_fake\")\n", "good_click_softsearch_carry_type_fake_emb1 = self.ssl_tower(good_click_softsearch_carry_type_fake_seq1, None, \"good_click_softsearch_carry_type_fake\")\n", "good_click_softsearch_carry_type_fake_emb2 = self.ssl_tower(good_click_softsearch_carry_type_fake_seq2, None, \"good_click_softsearch_carry_type_fake\")\n", "good_click_softsearch_price_fake_emb1 = self.ssl_tower(good_click_softsearch_price_fake_seq1, None, \"good_click_softsearch_price_fake\")\n", "good_click_softsearch_price_fake_emb2 = self.ssl_tower(good_click_softsearch_price_fake_seq2, None, \"good_click_softsearch_price_fake\")\n", "good_click_softsearch_item_count_fake_emb1 = self.ssl_tower(good_click_softsearch_item_count_fake_seq1, None, \"good_click_softsearch_item_count_fake\")\n", "good_click_softsearch_item_count_fake_emb2 = self.ssl_tower(good_click_softsearch_item_count_fake_seq2, None, \"good_click_softsearch_item_count_fake\")\n", "good_click_softsearch_topk_values_fake_emb1 = self.ssl_tower(good_click_softsearch_topk_values_fake_seq1, None, \"good_click_softsearch_topk_values_fake\")\n", "good_click_softsearch_topk_values_fake_emb2 = self.ssl_tower(good_click_softsearch_topk_values_fake_seq2, None, \"good_click_softsearch_topk_values_fake\")\n", "good_click_softsearch_topk_indices_fake_emb1 = self.ssl_tower(good_click_softsearch_topk_indices_fake_seq1, None, \"good_click_softsearch_topk_indices_fake\")\n", "good_click_softsearch_topk_indices_fake_emb2 = self.ssl_tower(good_click_softsearch_topk_indices_fake_seq2, None, \"good_click_softsearch_topk_indices_fake\")\n", "\n", "cl_loss += self.get_cl_loss(good_click_softsearch_item_id_fake_emb1, good_click_softsearch_item_id_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_seller_id_fake_emb1, good_click_softsearch_seller_id_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_lag_fake_emb1, good_click_softsearch_lag_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_category_fake_emb1, good_click_softsearch_category_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_carry_type_fake_emb1, good_click_softsearch_carry_type_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_price_fake_emb1, good_click_softsearch_price_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_item_count_fake_emb1, good_click_softsearch_item_count_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_topk_values_fake_emb1, good_click_softsearch_topk_values_fake_emb2)    +\\\n", "self.get_cl_loss(good_click_softsearch_topk_indices_fake_emb1, good_click_softsearch_topk_indices_fake_emb2)\n"]}], "source": ["for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{change_suffix(col, '_list_top100','_fake_seq1')}, {change_suffix(col, '_list_top100','_fake_seq2')} = self.mask_and_dropout({col}, {seq_len}, {dim})')\n", "    start_dim += dim\n", "\n", "print()\n", "for col in target:\n", "    dim = column2dim.get(col, 4)\n", "    print(f'{change_suffix(col, '_list_top100','_fake_emb1')} = self.ssl_tower({change_suffix(col, '_list_top100','_fake_seq1')}, None, \"{change_suffix(col, '_list_top100','_fake')}\")')\n", "    print(f'{change_suffix(col, '_list_top100','_fake_emb2')} = self.ssl_tower({change_suffix(col, '_list_top100','_fake_seq2')}, None, \"{change_suffix(col, '_list_top100','_fake')}\")')\n", "    start_dim += dim\n", "\n", "print()# ssl_tower_and_cl_loss\n", "\n", "\n", "cl_loss_str = 'cl_loss += '\n", "str_list = []\n", "for col in target:\n", "    str_list.append(f\"self.get_cl_loss({change_suffix(col, '_list_top100','_fake_emb1')}, {change_suffix(col, '_list_top100','_fake_emb2')})\")\n", "cl_loss_str += '    +\\\\\\n'.join(str_list)\n", "print(cl_loss_str)"]}, {"cell_type": "code", "execution_count": 177, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["good_click_softsearch_user_embedding = tf.concat([good_click_softsearch_item_id_list_top100,good_click_softsearch_seller_id_list_top100,good_click_softsearch_lag_list_top100,good_click_softsearch_category_list_top100,good_click_softsearch_carry_type_list_top100,good_click_softsearch_price_list_top100,good_click_softsearch_item_count_list_top100,good_click_softsearch_topk_values,good_click_softsearch_topk_indices], 2)\n"]}], "source": ["print(f'good_click_softsearch_user_embedding = tf.concat([{all_cols}], 2)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["修改建模方式 如 sumpooling或者attn\n", "然后加入dnn_input和soft_dnn_input"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}