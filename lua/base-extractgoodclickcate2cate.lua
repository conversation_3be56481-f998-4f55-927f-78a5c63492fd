""function cal_price(ori_price)
            local res = 0
            res = ori_price // 100
            if res >= 300 then res = 300
            end
            if res <0 then res = 0
            end
            return res
        end

        function cal_diff_price(ori_price, price)
            if ori_price == 0 then return 0
            end
            local slice = 100
            local res = ori_price - price
            res = res * slice // ori_price
            if res <0 then res = 0
            end
            if res >slice then res = slice
            end
            return res
        end

        function cal_view_time(view_time)
            local res = view_time
            if res <0 then res = 0
            end
            if res > 60 then res = 60
            end
            return res
        end

        function cal_cross(item1, item2)
            return tostring(item1).."_"..tostring(item2)
        end

        function calculate()
            local good_click_cate2cate_cate1_list = good_click_cate2cate_cate1_list or {}
            local good_click_cate2cate_cate2_list = good_click_cate2cate_cate2_list or {}
            local good_click_cate2cate_cate3_list = good_click_cate2cate_cate3_list or {}
            local good_click_cate2cate_category_list = good_click_cate2cate_category_list or {}
            local good_click_cate2cate_click_from_list = good_click_cate2cate_click_from_list or {}
            local good_click_cate2cate_click_index_list = good_click_cate2cate_click_index_list or {}
            local good_click_cate2cate_item_id_list = good_click_cate2cate_item_id_list or {}
            local good_click_cate2cate_lag_list = good_click_cate2cate_lag_list or {}
            local good_click_cate2cate_real_price_list = good_click_cate2cate_real_price_list or {}
            local good_click_cate2cate_real_seller_id_list = good_click_cate2cate_real_seller_id_list or {}
            local good_click_cate2cate_seller_id_list = good_click_cate2cate_seller_id_list or {}
            local good_click_cate2cate_timestamp_list = good_click_cate2cate_timestamp_list or {}

            local new_good_click_cate2cate_item_id_list_extend = {}
            local new_good_click_cate2cate_seller_id_list_extend = {}
            local new_good_click_cate2cate_real_seller_id_list_extend = {}
            local new_good_click_cate2cate_lag_list_extend = {}
            local new_good_click_cate2cate_cate1_list_extend = {}
            local new_good_click_cate2cate_cate2_list_extend = {}
            local new_good_click_cate2cate_cate3_list_extend = {}
            local new_good_click_cate2cate_category_list_extend = {}
            local new_good_click_cate2cate_carry_type_list_extend = {}
            local new_good_click_cate2cate_click_type_list_extend = {}
            local new_good_click_cate2cate_click_from_list_extend = {}
            local new_good_click_cate2cate_real_price_list_extend = {}
            local new_good_click_cate2cate_index_list_extend = {}
            local new_good_click_cate2cate_lag_hour_list_extend = {}
            local new_good_click_cate2cate_lag_min_list_extend = {}
            local new_good_click_cate2cate_seller_id_lag_list_extend = {}
            local new_good_click_cate2cate_cate1_lag_list_extend = {}
            local new_good_click_cate2cate_cate2_lag_list_extend = {}
            local new_good_click_cate2cate_cate3_lag_list_extend = {}
            local new_good_click_cate2cate_seller_id_price_list_extend = {}
            local new_good_click_cate2cate_cate1_price_list_extend = {}
            local new_good_click_cate2cate_cate2_price_list_extend = {}
            local new_good_click_cate2cate_cate3_price_list_extend = {}

            local count = 0
            local len = #good_click_cate2cate_item_id_list
            for index=len,1,-1 do
                table.insert(new_good_click_cate2cate_item_id_list_extend, good_click_cate2cate_item_id_list[index])
                table.insert(new_good_click_cate2cate_seller_id_list_extend, good_click_cate2cate_seller_id_list[index])
                table.insert(new_good_click_cate2cate_real_seller_id_list_extend, good_click_cate2cate_real_seller_id_list[index])
                table.insert(new_good_click_cate2cate_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24))
                table.insert(new_good_click_cate2cate_cate1_list_extend, good_click_cate2cate_cate1_list[index])
                table.insert(new_good_click_cate2cate_cate2_list_extend, good_click_cate2cate_cate2_list[index])
                table.insert(new_good_click_cate2cate_cate3_list_extend, good_click_cate2cate_cate3_list[index])
                table.insert(new_good_click_cate2cate_category_list_extend, good_click_cate2cate_category_list[index])
                table.insert(new_good_click_cate2cate_carry_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>16) & 0xff)
                table.insert(new_good_click_cate2cate_click_type_list_extend, (good_click_cate2cate_click_flow_type_list[index]>>24) & 0xff)
                table.insert(new_good_click_cate2cate_click_from_list_extend, good_click_cate2cate_click_from_list[index])
                table.insert(new_good_click_cate2cate_real_price_list_extend, cal_price(good_click_cate2cate_real_price_list[index]))
                table.insert(new_good_click_cate2cate_index_list_extend, count)
                table.insert(new_good_click_cate2cate_lag_hour_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600))
                table.insert(new_good_click_cate2cate_lag_min_list_extend, (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(60))
                table.insert(new_good_click_cate2cate_seller_id_lag_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))
                table.insert(new_good_click_cate2cate_cate1_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff,
                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))
                table.insert(new_good_click_cate2cate_cate2_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff,
                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))
                table.insert(new_good_click_cate2cate_cate3_lag_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff,
                            (_REQ_TIME_ // 1000 - good_click_cate2cate_timestamp_list[index])//(3600*24)))
                table.insert(new_good_click_cate2cate_seller_id_price_list_extend, cal_cross(good_click_cate2cate_seller_id_list[index], cal_price(good_click_cate2cate_real_price_list[index])))
                table.insert(new_good_click_cate2cate_cate1_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 48) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))
                table.insert(new_good_click_cate2cate_cate2_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 32) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))
                table.insert(new_good_click_cate2cate_cate3_price_list_extend, cal_cross((good_click_cate2cate_category_list[index] >> 16) & 0xffff, cal_price(good_click_cate2cate_real_price_list[index])))

                count = count + 1
                if count >= 100 then
                    break
                end
            end

            return new_good_click_cate2cate_item_id_list_extend, new_good_click_cate2cate_seller_id_list_extend, new_good_click_cate2cate_real_seller_id_list_extend,
                new_good_click_cate2cate_lag_list_extend, new_good_click_cate2cate_cate1_list_extend, new_good_click_cate2cate_cate2_list_extend,
                new_good_click_cate2cate_cate3_list_extend, new_good_click_cate2cate_category_list_extend, new_good_click_cate2cate_carry_type_list_extend,
                new_good_click_cate2cate_click_type_list_extend, new_good_click_cate2cate_click_from_list_extend, new_good_click_cate2cate_real_price_list_extend,
                new_good_click_cate2cate_index_list_extend, new_good_click_cate2cate_lag_hour_list_extend, new_good_click_cate2cate_lag_min_list_extend,
                new_good_click_cate2cate_seller_id_lag_list_extend, new_good_click_cate2cate_cate1_lag_list_extend, new_good_click_cate2cate_cate2_lag_list_extend,
                new_good_click_cate2cate_cate3_lag_list_extend, new_good_click_cate2cate_seller_id_price_list_extend, new_good_click_cate2cate_cate1_price_list_extend,
                new_good_click_cate2cate_cate2_price_list_extend, new_good_click_cate2cate_cate3_price_list_extend
        end",
           
