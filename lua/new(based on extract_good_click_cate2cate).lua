function cal_price(ori_price)
    local res = 0
    res = ori_price // 100
    if res >= 300 then res = 300
    end
    if res <0 then res = 0
    end
    return res
end


function cal_view_time(view_time)
    local res = view_time
    if res <0 then res = 0
    end
    if res > 300 then res = 300
    end
    return res
end

function hour_bucket(hour)
    local val = math.floor(math.log(hour / 2 + 1.0) * 10)
    if val > 200 then4
        return 200
    else
        return val
    end
end

function min_bucket(min)
    if min < 1 then
        return 1
    elseif min < 3 then
        return 3
    elseif min < 5 then
        return 5
    elseif min < 10 then
        return 10
    elseif min <= 60 * 12 then
        return ((min + 15) // 30 + 1) * 30
    elseif min <= 60 * 24 then
        return ((min + 45) // 90 + 1) * 90
    elseif min <= 60 * 24 * 7 then
        return ((min + 90) // 180 + 1) * 180
    else
        return 999999
    end
end
-- function cal_cross(item1, item2)
--     return tostring(item1).."_"..tostring(item2)
-- end

function calculate()
    local good_click_item_id_list = good_click_item_id_list_reverse or {}
    local good_click_seller_id_list = good_click_seller_id_list_reverse or {}
    local good_click_real_seller_id_list = good_click_real_seller_id_list_reverse or {}
    local good_click_timestamp_list = good_click_timestamp_list_reverse or {}
    local good_click_category_list = good_click_category_list_reverse or {}
    local good_click_flow_type_list = good_click_flow_type_list_reverse or {}
    local good_click_from_list = good_click_from_list_reverse or {}
    local good_click_price_list = good_click_price_list_reverse or {}
    local good_detail_page_view_time_list = good_detail_page_view_time_list_reverse or {}
    local good_click_origin_price_list = good_click_origin_price_list_reverse or {}
    local good_click_label_list = good_click_label_list_reverse or {}
    local good_click_uniform_spu_id_list = good_click_uniform_spu_id_list_reverse or {}
    local good_click_item_count_list = good_click_item_count_list_reverse or {}

    local new_good_click_item_id_list_extend = {}
    local new_good_click_seller_id_list_extend = {}
    local new_good_click_real_seller_id_list_extend = {}
    local new_good_click_lag_list_extend = {}
    local new_good_click_cate1_list_extend = {}
    local new_good_click_cate2_list_extend = {}
    local new_good_click_cate3_list_extend = {}
    local new_good_click_category_list_extend = {}
    local new_good_click_carry_type_list_extend = {}
    local new_good_click_click_type_list_extend = {}
    local new_good_click_from_list_extend = {}
    local new_good_click_price_list_extend = {}
    local new_good_click_origin_price_list_extend = {}
    local new_good_click_page_view_time_list_extend = {}
    local new_good_click_price_diff_list_extend = {}
    local new_good_click_label_list_extend = {}
    local new_good_click_uniform_spu_id_list_extend = {}
    local new_good_click_item_count_list_extend = {}
    -- replace index list here, because we have topk indices

    local new_good_click_lag_hour_list_extend = {}
    local new_good_click_lag_min_list_extend = {}
    -- local new_good_click_seller_id_lag_list_extend = {}
    -- local new_good_click_cate1_lag_list_extend = {}
    -- local new_good_click_cate2_lag_list_extend = {}
    -- local new_good_click_cate3_lag_list_extend = {}
    -- local new_good_click_seller_id_price_list_extend = {}
    -- local new_good_click_cate1_price_list_extend = {}
    -- local new_good_click_cate2_price_list_extend = {}
    -- local new_good_click_cate3_price_list_extend = {}
    -- local new_good_click_seller_id_view_list_extend = {}
    -- local new_good_click_cate1_view_list_extend = {}
    -- local new_good_click_cate2_view_list_extend = {}
    -- local new_good_click_cate3_view_list_extend = {}
    

    local len = #good_click_topk_indices
    local index = 0
    for i=1,len,1 do
        index = good_click_topk_indices[i] + 1
        table.insert(new_good_click_item_id_list_extend, good_click_item_id_list[index])
        table.insert(new_good_click_seller_id_list_extend, good_click_seller_id_list[index])
        table.insert(new_good_click_real_seller_id_list_extend, good_click_real_seller_id_list[index])
        table.insert(new_good_click_lag_list_extend, (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24))
        table.insert(new_good_click_cate1_list_extend, (good_click_category_list[index] >> 48) & 0xffff)
        table.insert(new_good_click_cate2_list_extend, (good_click_category_list[index] >> 32) & 0xffff)
        table.insert(new_good_click_cate3_list_extend, (good_click_category_list[index] >> 16) & 0xffff)
        table.insert(new_good_click_category_list_extend, good_click_category_list[index])
        table.insert(new_good_click_carry_type_list_extend, (good_click_flow_type_list[index]>>16) & 0xff)
        table.insert(new_good_click_click_type_list_extend, (good_click_flow_type_list[index]>>24) & 0xff)
        table.insert(new_good_click_from_list_extend, good_click_from_list[index])
        table.insert(new_good_click_price_list_extend, cal_price(good_click_price_list[index]))

        table.insert(new_good_click_page_view_time_list_extend, cal_view_time(good_detail_page_view_time_list[index]))
        table.insert(new_good_click_label_list_extend, good_click_label_list[index])
        table.insert(new_good_click_uniform_spu_id_list_extend, good_click_uniform_spu_id_list[index])
        table.insert(new_good_click_item_count_list_extend, good_click_item_count_list[index])

        table.insert(new_good_click_lag_hour_list_extend, hour_bucket((_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600)))
        table.insert(new_good_click_lag_min_list_extend, min_bucket((_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(60)))
        -- table.insert(new_good_click_seller_id_lag_list_extend, cal_cross(good_click_seller_id_list[index], (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))
        -- table.insert(new_good_click_cate1_lag_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff,
        --             (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))
        -- table.insert(new_good_click_cate2_lag_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff,
        --             (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))
        -- table.insert(new_good_click_cate3_lag_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff,
        --             (_REQ_TIME_ // 1000 - good_click_timestamp_list[index])//(3600*24)))
        -- table.insert(new_good_click_seller_id_price_list_extend, cal_cross(good_click_seller_id_list[index], cal_price(good_click_origin_price_list[index])))
        -- table.insert(new_good_click_cate1_price_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_price(good_click_origin_price_list[index])))
        -- table.insert(new_good_click_cate2_price_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_price(good_click_origin_price_list[index])))
        -- table.insert(new_good_click_cate3_price_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_price(good_click_origin_price_list[index])))
        
        -- table.insert(new_good_click_seller_id_view_list_extend, cal_cross(good_click_seller_id_list[index], cal_view_time(good_detail_page_view_time_list[index])))
        -- table.insert(new_good_click_cate1_view_list_extend, cal_cross((good_click_category_list[index] >> 48) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))
        -- table.insert(new_good_click_cate2_view_list_extend, cal_cross((good_click_category_list[index] >> 32) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))
        -- table.insert(new_good_click_cate3_view_list_extend, cal_cross((good_click_category_list[index] >> 16) & 0xffff, cal_view_time(good_detail_page_view_time_list[index])))

    end
    return new_good_click_item_id_list_extend, new_good_click_seller_id_list_extend, new_good_click_real_seller_id_list_extend, new_good_click_lag_list_extend,
    new_good_click_cate1_list_extend, new_good_click_cate2_list_extend,
    new_good_click_cate3_list_extend, new_good_click_category_list_extend,new_good_click_carry_type_list_extend, new_good_click_click_type_list_extend,
    new_good_click_from_list_extend, new_good_click_price_list_extend,
    new_good_click_page_view_time_list_extend, new_good_click_label_list_extend,
    new_good_click_uniform_spu_id_list_extend, new_good_click_item_count_list_extend,
    new_good_click_lag_hour_list_extend, new_good_click_lag_min_list_extend,
    -- new_good_click_seller_id_lag_list_extend, new_good_click_cate1_lag_list_extend, new_good_click_cate2_lag_list_extend, new_good_click_cate3_lag_list_extend,
    -- new_good_click_seller_id_price_list_extend, new_good_click_cate1_price_list_extend, new_good_click_cate2_price_list_extend, new_good_click_cate3_price_list_extend,
    -- new_good_click_seller_id_view_list_extend, new_good_click_cate1_view_list_extend, new_good_click_cate2_view_list_extend, new_good_click_cate3_view_list_extend
end