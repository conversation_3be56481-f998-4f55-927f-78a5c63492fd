# 输入四个数据
# last_field: 当前kai feature文件中最后一个field，用于生成自增field
# schema 添加到fg_schema_out中的部分，用于解析column名字
# copy_attr_slot: 在数据流中将slot后缀的特征复制成column名的特征的attr字段，用于解析column对应slot号
# item_map_list: extract_kuiba或者remap_slot中的item_map，用于解析每个slot对应的size
last_field = 622
# schema = """
# column_name = good_show_soft_category1, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_category2, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_category3, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_category4, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_commodity_id, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_detail_content_stay_time, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_exposure_ratio, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_exposure_time, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_hour_of_day, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_leaf_category, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_pagecode_id, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_seller_id, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_time_gap_hour, compression_type = None, column_type = List<Long>
# column_name = good_show_soft_uniform_spu_id, compression_type = None, column_type = List<Long>
# """.strip().splitlines()

copy_attr_slot = [
            {
              "from_item": "good_show_soft_120",
              "to_item": "good_show_soft_commodity_id"
            },
            {
              "from_item": "good_show_soft_121",
              "to_item": "good_show_soft_pagecode_id"
            },
            {
              "from_item": "good_show_soft_122",
              "to_item": "good_show_soft_uniform_spu_id"
            },
            {
              "from_item": "good_show_soft_123",
              "to_item": "good_show_soft_exposure_ratio"
            },
            {
              "from_item": "good_show_soft_124",
              "to_item": "good_show_soft_exposure_time"
            },
            {
              "from_item": "good_show_soft_125",
              "to_item": "good_show_soft_detail_content_stay_time"
            },
            {
              "from_item": "good_show_soft_126",
              "to_item": "good_show_soft_seller_id"
            },
            {
              "from_item": "good_show_soft_127",
              "to_item": "good_show_soft_hour_of_day"
            },
            {
              "from_item": "good_show_soft_128",
              "to_item": "good_show_soft_time_gap_hour"
            },
            {
              "from_item": "good_show_soft_129",
              "to_item": "good_show_soft_category1"
            },
            {
              "from_item": "good_show_soft_130",
              "to_item": "good_show_soft_category2"
            },
            {
              "from_item": "good_show_soft_131",
              "to_item": "good_show_soft_category3"
            },
            {
              "from_item": "good_show_soft_132",
              "to_item": "good_show_soft_category4"
            },
            {
              "from_item": "good_show_soft_133",
              "to_item": "good_show_soft_leaf_category"
            }
          ]
item_map_list = [
            [
              "120",
              120,
              120,
              50000001
            ],
            [
              "121",
              121,
              121,
              1001
            ],
            [
              "122",
              122,
              122,
              20000001
            ],
            [
              "123",
              123,
              123,
              1001
            ],
            [
              "124",
              124,
              124,
              1001
            ],
            [
              "125",
              125,
              125,
              1001
            ],
            [
              "126",
              126,
              126,
              40001
            ],
            [
              "127",
              127,
              127,
              1001
            ],
            [
              "128",
              128,
              128,
              20001
            ],
            [
              "129",
              129,
              129,
              40001
            ],
            [
              "130",
              130,
              130,
              40001
            ],
            [
              "131",
              131,
              131,
              40001
            ],
            [
              "132",
              132,
              132,
              40001
            ],
            [
              "133",
              133,
              133,
              40001
            ]
          ]

# 解析 slot对应size
slot2size = {}
for l in item_map_list:
    slot2size[l[1]] = l[3]

# 重要特征的dim
column2dim = {
    "good_show_soft_commodity_id":16,
    "good_show_soft_seller_id":16,
    "good_show_soft_uniform_spu_id":16
}

# 解析column名字对应slot， 并且顺序生成field
result = []
column2slot = {}
for d in copy_attr_slot:
    col = None
    slot = None
    for k,v in d.items():
        if 'from_' in k:
            slot = v
        elif 'to_' in k:
            col = v
        else:
            raise ValueError
    column2slot[col] = int(slot.split('_')[-1])
    column_name = col

    # column_name, compression_type, column_type = [i.split('=')[1].strip() for i in row.split(',')]
    kai2_feature = f'class={column_name}, category=user, field={last_field+1}, size=0, no_default=1, topic_id=0'
    slot = column2slot[column_name]
    size = slot2size[slot]
    dim = column2dim.get(column_name, 4)
    # 为了避免mapslot和remapslot重复，需要添加offset
    remap_slot = slot + 800
    map_slot = remap_slot
    feature_map_for_ps = f'remap_slot={remap_slot}, dim={dim}, map_slot={map_slot}, size=0, slot=0, name={column_name}, category=ad, field={last_field+1}, feature_size={size}, topic_id=0'
    kai2_emb = f"{column_name} = kai.new_embedding('{column_name}', dim={dim}, slots=[{remap_slot}], expand=100)"
    result.append((slot, kai2_feature, feature_map_for_ps, kai2_emb))
    last_field += 1

kai2_feature = [i[1] for i in result]
feature_map_for_ps = [i[2] for i in result]
kai2_emb = [i[3] for i in result]
print('\n'.join(kai2_feature))
print()
print('\n'.join(feature_map_for_ps))
print()
print('\n'.join(kai2_emb)) # expand之后得到的emb是[B, dim * seq]的二维张量
print()
#### 接下来是dnn input
all_cols = ','.join(column2slot.keys())
print(all_cols)
print()
#### 接下来是model_def
block_start = 84
for col in column2slot.keys():
    print(f'{col} = block_data[{block_start}].output')
    block_start += 1
print()
# good_show_soft_user_embedding是沿着dim*seq维度拼接的，再slice和reshape为各条特征
seq_len = 100
start_dim = 0
for col in column2slot.keys():
    dim = column2dim.get(col, 4)
    print(f'{col} = tf.slice(good_show_soft_user_embedding, [0, {start_dim}], [-1, {dim * seq_len}])')
    start_dim += dim * seq_len
    block_start += 1
print()
for col in column2slot.keys():
    dim = column2dim.get(col, 4)
    print(f'{col} = tf.reshape({col}, [-1, {seq_len}, {dim}])')
    start_dim += dim
    block_start += 1
print()
# 再按照[B, Seq, Dim]在最后一位拼接dim
print(f'good_show_soft_user_embedding = tf.concat([{all_cols}], 2)')

# ssl_tower_and_cl_loss
cl_loss_str = 'cl_loss += '
str_list = []
for col in column2slot.keys():
    str_list.append(f'self.ssl_tower_and_cl_loss({col}, "{col}_cl")')
cl_loss_str += '    +\\\n'.join(str_list)
print(cl_loss_str)