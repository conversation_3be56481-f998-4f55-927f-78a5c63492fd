import json

before_attr = ["merchant_good_exposure_ratio_list",
              "merchant_good_commodity_id_list",
              "merchant_good_pagecode_id_list",
              "merchant_good_timestamp_list",
              "merchant_good_timestamp_ms_list",
              "merchant_good_uniform_spu_id_list"]

after_attr = [s.replace('merchant_good_', 'origin_colossus_rs_') for s in before_attr]
after_attr[1] = "origin_colossus_rs_item_id_list"

#######

before_attr = [
              "good_show_soft_120",
              "good_show_soft_121",
              "good_show_soft_122",
              "good_show_soft_123",
              "good_show_soft_124",
              "good_show_soft_125",
              "good_show_soft_126",
              "good_show_soft_127",
              "good_show_soft_128",
              "good_show_soft_129",
              "good_show_soft_130",
              "good_show_soft_131",
              "good_show_soft_132",
              "good_show_soft_133"
            ]
after_attr = [
              "good_show_category1",
              "good_show_category2",
              "good_show_category3",
              "good_show_category4",
              "good_show_commodity_id",
              "good_show_detail_content_stay_time",
              "good_show_exposure_ratio",
              "good_show_exposure_time",
              "good_show_hour_of_day",
              "good_show_leaf_category",
              "good_show_pagecode_id",
              "good_show_seller_id",
              "good_show_time_gap_hour",
              "good_show_uniform_spu_id"
            ]
assert len(before_attr)==len(after_attr)

jsonlist = [{"from_item": before_attr[i], 
             "to_item": after_attr[i].replace('good_show_','good_show_soft_')} for i in range(len(before_attr))]
print(json.dumps(jsonlist, indent=2))