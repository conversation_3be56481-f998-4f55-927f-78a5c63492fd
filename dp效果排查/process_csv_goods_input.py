import csv
from collections import Counter

def process_csv(file_path):
    # 存储不为0的merchant_product_id
    non_zero_merchant_ids = []
    
    total_count = 0
    both_q_and_k = 0
    all_basicattr = []
    dedup_basicattr = []
    all_brand = []
    dedup_brand = []

    cnt_matrix = [[0,0],[0,0]] # [0][0]都没有,[0][1]没有l有l2,[1][0]有l没有l2,[1][1]都有
    # 读取CSV文件
    with open(file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            user_id = row.get('user_id', '0')
            merchant_id = row.get('merchant_product_id', '0')
            extractitemgoodsbasicattr = row.get('extractitemgoodsbasicattr', '')
            extractitemgoodsbrand = row.get('extractitemgoodsbasicattr', '')
            l = None
            l2 = None
            if extractitemgoodsbasicattr != '':
                l = eval(extractitemgoodsbasicattr)
                all_basicattr.append(len(l))
                dedup_basicattr.append(len(set(l)))
            if extractitemgoodsbrand != '':
                l2 = eval(extractitemgoodsbrand)
                all_brand.append(len(l2))
                dedup_brand.append(len(set(l2)))
            if l and l2:
                cnt_matrix[1][1] += 1
            elif l and not l2:
                cnt_matrix[1][0] += 1
            elif not l and l2:
                cnt_matrix[0][1] += 1
            else:
                cnt_matrix[0][0] += 1
            

            # 筛选不为0的merchant_product_id
            if merchant_id != '0' and user_id != '0':
                non_zero_merchant_ids.append((int(user_id), int(merchant_id)))
                if l and l2:
                    both_q_and_k += 1
            
            total_count += 1
    print(cnt_matrix)
    print('basicattr avg len', sum(all_basicattr)/len(all_basicattr), sum(dedup_basicattr)/len(dedup_basicattr))
    print('brand avg len', sum(all_brand)/len(all_brand), sum(dedup_brand)/len(dedup_brand))
    print('both q and k ',both_q_and_k)
    # # 统计出现超过3次的good_show_soft_commodity_id
    # counter = Counter(commodity_ids)
    # frequent_ids = {id: count for id, count in counter.items() if count > 3}
    # print(f'总共{total_count}行')

    return non_zero_merchant_ids

if __name__ == "__main__":
    # file_path = "短带取训练数据_Snippet 1_23817459.csv"  # 替换为你的CSV文件路径
    file_path = "/Users/<USER>/Downloads/2025-06-10_1_Snippet 1_23842053.csv"
    non_zero_ids = process_csv(file_path)
    
    # print(f"找到 {len(non_zero_ids)} 个不为0的merchant_product_id")
    # print(f"good_show_soft_commodity_id不为空的比例: {empty_ratio:.2%}")
    # print(f"出现超过3次的good_show_soft_commodity_id:")
    # for id, count in sorted(frequent_ids.items(), key=lambda x: x[1], reverse=True):
    #     print(f"  ID: {id}, 出现次数: {count}")

    # # 将不为0的merchant_product_id保存到文件
    # with open("non_zero_merchant_ids.txt", "w", encoding="utf-8") as f:
    #     for id in non_zero_ids:
    #         f.write(f"{id},\n")