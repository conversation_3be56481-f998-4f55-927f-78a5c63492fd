import re
import statistics

def parse_log_file(log_path):
    """解析日志文件，提取需要的列表数据，支持多个lua_result部分"""
    with open(log_path, 'r', encoding='utf-8') as f:
        log_content = f.read()

    # 查找所有lua_result部分
    lua_result_pattern = r'log_tag: lua_result[\s\S]*?(?=log_tag:|$)'
    lua_results = re.findall(lua_result_pattern, log_content, re.DOTALL)

    if not lua_results:
        print("未找到任何lua_result部分")
        return [], [], [], []

    print(f"找到 {len(lua_results)} 个lua_result部分")

    all_commodity_lists = []
    all_indices_lists = []
    all_values_lists = []
    all_soft_lists = []

    for i, remap_content in enumerate(lua_results):
        print(f"\n处理第 {i+1} 个lua_result部分:")

        # 分别提取common_attr和item_attr部分
        common_attr_pattern = r'\[common_attr\](.*?)(?=\[item_attr\]|$)'
        common_attr_match = re.search(common_attr_pattern, remap_content, re.DOTALL)

        item_attr_pattern = r'\[item_attr\][\s\S]*?total item num: \d+[\s\S]*?\{(.*?)\}'
        item_attr_match = re.search(item_attr_pattern, remap_content, re.DOTALL)

        if not common_attr_match:
            print(f"  未找到common_attr部分")
            continue

        if not item_attr_match:
            print(f"  未找到item_attr部分")
            continue

        common_attr_content = common_attr_match.group(1)
        item_attr_content = item_attr_match.group(1)

        # 从common_attr中提取good_show_commodity_id_list_100
        commodity_pattern = r'good_show_commodity_id_list_100 \(int_list\[(\d+)\]\): \[(.*?)\]'
        commodity_match = re.search(commodity_pattern, common_attr_content, re.DOTALL)

        # 从item_attr中提取其他数据
        indices_pattern = r'topk_indices \(int_list\[(\d+)\]\): \[(.*?)\]'
        indices_match = re.search(indices_pattern, item_attr_content, re.DOTALL)

        values_pattern = r'topk_values \(double_list\[(\d+)\]\): \[(.*?)\]'
        values_match = re.search(values_pattern, item_attr_content, re.DOTALL)

        soft_pattern = r'selected_ids \(int_list\[(\d+)\]\): \[(.*?)\]'
        soft_match = re.search(soft_pattern, item_attr_content, re.DOTALL)

        if not commodity_match or not indices_match or not values_match or not soft_match:
            print(f"  第 {i+1} 个lua_result部分缺少必要数据")
            print(f"    commodity_match: {bool(commodity_match)}")
            print(f"    indices_match: {bool(indices_match)}")
            print(f"    values_match: {bool(values_match)}")
            print(f"    soft_match: {bool(soft_match)}")
            continue

        # 解析列表数据
        try:
            commodity_list = [int(x.strip()) for x in commodity_match.group(2).split(',')]
            indices_list = [int(x.strip()) for x in indices_match.group(2).split(',')]
            values_list = [float(x.strip()) for x in values_match.group(2).split(',')]
            soft_list = [int(x.strip()) for x in soft_match.group(2).split(',')]

            print(f"  成功解析第 {i+1} 个lua_result的数据:")
            print(f"    - good_show_commodity_id_list_100: {len(commodity_list)}个元素")
            print(f"    - topk_indices: {len(indices_list)}个元素")
            print(f"    - topk_values: {len(values_list)}个元素")
            print(f"    - selected_ids: {len(soft_list)}个元素")

            all_commodity_lists.append(commodity_list)
            all_indices_lists.append(indices_list)
            all_values_lists.append(values_list)
            all_soft_lists.append(soft_list)

        except Exception as e:
            print(f"  解析第 {i+1} 个lua_result的列表数据时出错: {e}")
            continue

    if not all_commodity_lists:
        print("未能成功解析任何lua_result数据")
        return [], [], [], []

    return all_commodity_lists, all_indices_lists, all_values_lists, all_soft_lists

def verify_lists(commodity_list, indices_list, soft_list):
    """验证通过topk_indices从commodity_list中取出的元素是否与soft_list相同"""
    if not commodity_list or not indices_list or not soft_list:
        return False

    # 根据indices从commodity_list中取元素
    selected_commodities = []
    for idx in indices_list:
        if 0 <= idx < len(commodity_list):
            selected_commodities.append(commodity_list[idx])
        else:
            print(f"    索引越界: {idx}, commodity_list长度: {len(commodity_list)}")
            return False

    # 验证差值是否一致
    if len(selected_commodities) != len(soft_list):
        print(f"    列表长度不匹配: selected_commodities={len(selected_commodities)}, soft_list={len(soft_list)}")
        return False

    if len(selected_commodities) == 0:
        return True

    # 计算第一个位置的差值作为基准
    base_diff = selected_commodities[0] - soft_list[0]
    print(f"    基准差值: {base_diff}")

    is_consistent = True
    inconsistent_count = 0

    for i, (a, b) in enumerate(zip(selected_commodities, soft_list)):
        current_diff = a - b
        if current_diff != base_diff:
            if inconsistent_count < 5:  # 只显示前5个不一致的
                print(f"    位置 {i}: selected_commodity={a}, soft_list={b}, 差值={current_diff} (期望差值={base_diff})")
            inconsistent_count += 1
            is_consistent = False
            if inconsistent_count == 5:
                print("    ...")

    if inconsistent_count > 0:
        print(f"    总共有 {inconsistent_count} 个位置的差值不一致")

    return is_consistent

def verify_all_lists(all_commodity_lists, all_indices_lists, all_soft_lists):
    """验证所有lua_result部分的数据"""
    if not all_commodity_lists:
        print("没有数据需要验证")
        return []

    results = []
    for i in range(len(all_commodity_lists)):
        print(f"\n验证第 {i+1} 个lua_result部分:")
        commodity_list = all_commodity_lists[i]
        indices_list = all_indices_lists[i]
        soft_list = all_soft_lists[i]

        is_consistent = verify_lists(commodity_list, indices_list, soft_list)
        if is_consistent:
            print(f"  验证结果: 差值一致 ✓")
        else:
            print(f"  验证结果: 差值不一致 ✗")

        results.append(is_consistent)

    return results

def calculate_stats(all_commodity_lists, all_soft_lists):
    """计算所有列表的统计信息"""
    if not all_commodity_lists:
        return []

    stats = []
    for i in range(len(all_commodity_lists)):
        commodity_len = len(all_commodity_lists[i])
        commodity_unique_len = len(set(all_commodity_lists[i]))  # 去重后的长度
        soft_len = len(all_soft_lists[i])
        soft_unique_len = len(set(all_soft_lists[i]))  # selected_ids去重后的长度
        stats.append((commodity_len, commodity_unique_len, soft_len, soft_unique_len))

    return stats

def calculate_coverage(all_commodity_lists, all_soft_lists):
    """计算所有列表的统计信息"""
    if not all_commodity_lists:
        return []

    stats1 = []
    stats2 = []
    stats3 = []
    for i in range(len(all_commodity_lists)):
        stats1.append(len(set(all_commodity_lists[i]) - set(all_soft_lists[i])))
        stats2.append(len(set(all_soft_lists[i]) - set(all_commodity_lists[i])))
        stats3.append(len(set(all_commodity_lists[i]) & set(all_soft_lists[i])))
    print('in 100 not in soft:', sum(stats1)/len(stats1))
    print('in soft not in 100:', sum(stats2)/len(stats2))
    print('in 100 and soft:', sum(stats3)/len(stats3))

    return stats1, stats2

def main():
    log_path = "playground/test4.log"

    # 解析日志文件
    all_commodity_lists, all_indices_lists, all_values_lists, all_soft_lists = parse_log_file(log_path)

    if not all_commodity_lists:
        print("解析失败，无法继续")
        return

    print(f"\n总共解析到 {len(all_commodity_lists)} 个lua_result部分的数据")

    # 验证所有列表
    # print("\n" + "="*60)
    # print("验证topk_indices从commodity_list中取出的元素与soft_list的差值是否一致:")
    # verification_results = verify_all_lists(all_commodity_lists, all_indices_lists, all_soft_lists)

    # 计算统计信息
    print("\n" + "="*60)
    print("统计信息:")
    stats = calculate_stats(all_commodity_lists, all_soft_lists)
    
    for i, (commodity_len, commodity_unique_len, soft_len, soft_unique_len) in enumerate(stats):
        print(f"\n第 {i+1} 个lua_result部分:")
        print(f"  good_show_commodity_id_list_100 长度: {commodity_len}")
        print(f"  good_show_commodity_id_list_100 去重后长度: {commodity_unique_len}")
        print(f"  selected_ids 长度: {soft_len}")
        print(f"  selected_ids 去重后长度: {soft_unique_len}")
        # print(f"  差值验证结果: {'通过' if verification_results[i] else '失败'}")

        # 计算commodity_list重复率
        if commodity_len > 0:
            commodity_duplicate_rate = (commodity_len - commodity_unique_len) / commodity_len * 100
            print(f"  good_show_commodity_id_list_100 重复率: {commodity_duplicate_rate:.1f}% ({commodity_len - commodity_unique_len} 个重复项)")

        # 计算soft_list重复率
        if soft_len > 0:
            soft_duplicate_rate = (soft_len - soft_unique_len) / soft_len * 100
            print(f"  selected_ids 重复率: {soft_duplicate_rate:.1f}% ({soft_len - soft_unique_len} 个重复项)")
    
    # 总结
    print("\n" + "="*60)
    print("总结:")
    stats1, stats2 = calculate_coverage(all_commodity_lists, all_soft_lists)
    # 计算平均good_show_commodity_id_list_100 长度和去重长度
    # 计算平均selected_ids 长度和去重长度
    commodity_lens, commodity_unique_lens, soft_lens, soft_unique_lens = zip(*stats)
    print("commodity_lens:",commodity_lens)
    avg_commodity_len = sum(commodity_lens) / len(commodity_lens)
    avg_commodity_unique_len = sum(commodity_unique_lens) / len(commodity_unique_lens)
    avg_soft_len = sum(soft_lens) / len(soft_lens)
    avg_soft_unique_len = sum(soft_unique_lens) / len(soft_unique_lens)
    print(f"平均 good_show_commodity_id_list_100 长度: {avg_commodity_len:.1f}")
    print(f"平均 good_show_commodity_id_list_100 去重后长度: {avg_commodity_unique_len:.1f}，比例{avg_commodity_unique_len/avg_commodity_len:.2f}")
    print(f"长度超过100的去重序列个数：{len([i for i in commodity_unique_lens if i > 100])} / {len(commodity_unique_lens)}")
    print(f"平均 selected_ids 长度: {avg_soft_len:.1f}")
    print(f"平均 selected_ids 去重后长度: {avg_soft_unique_len:.1f}，比例{avg_soft_unique_len/avg_soft_len:.2f}")
    # total_passed = sum(verification_results)
    # total_sections = len(verification_results)
    # print(f"总共 {total_sections} 个lua_result部分")
    # print(f"差值验证通过: {total_passed} 个")
    # print(f"差值验证失败: {total_sections - total_passed} 个")

    # if total_passed == total_sections:
    #     print("✓ 所有lua_result部分的差值验证都通过")
    # else:
    #     print("✗ 存在差值验证失败的lua_result部分")

if __name__ == "__main__":
    main()
