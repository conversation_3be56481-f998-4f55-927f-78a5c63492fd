import csv
from collections import Counter

def process_csv(file_path):
    # 存储不为0的merchant_product_id
    non_zero_merchant_ids = []
    
    # 统计good_show_soft_commodity_id
    commodity_ids = []
    total_count = 0
    non_empty_count = 0
    all_soft = []
    dedup_soft = []
    all_hard = []
    dedup_hard = []
    hard100 = []
    dedup_hard100 = []
    all_good_click = []
    dedup_good_click = []
    cnt_matrix = [[0,0],[0,0]] # [0][0]都没有,[0][1]没有l有l2,[1][0]有l没有l2,[1][1]都有
    # 读取CSV文件
    with open(file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            user_id = row.get('user_id', '0')
            merchant_id = row.get('merchant_product_id', '0')
            commodity_id = row.get('good_show_soft_commodity_id', '')
            colossus_rs_id = row.get('colossus_rs_item_id_list', '')
            good_click_id = row.get('good_click_cate2cate_item_id_list_extend_new', '')
            l = None
            l2 = None
            if commodity_id != '':
                l = eval(commodity_id)
                all_soft.append(len(l))
                dedup_soft.append(len(set(l)))
            if colossus_rs_id != '':
                l2 = eval(colossus_rs_id)
                all_hard.append(len(l2))
                dedup_hard.append(len(set(l2)))
                hard100.append(len(l2[:100]))
                dedup_hard100.append(len(set(l2[:100])))
            if good_click_id != '':
                l3 = eval(good_click_id)
                all_good_click.append(len(l3))
                dedup_good_click.append(len(set(l3)))
            if l and l2:
                cnt_matrix[1][1] += 1
            elif l and not l2:
                cnt_matrix[1][0] += 1
            elif not l and l2:
                cnt_matrix[0][1] += 1
            else:
                cnt_matrix[0][0] += 1

            # 筛选不为0的merchant_product_id
            if merchant_id != '0' and user_id != '0':
                non_zero_merchant_ids.append((int(user_id), int(merchant_id)))
            
            # 统计good_show_soft_commodity_id不为空的比例
            total_count += 1
    print(cnt_matrix)
    print('soft avg len', sum(all_soft)/len(all_soft), sum(dedup_soft)/len(dedup_soft))
    print('hard avg len', sum(all_hard)/len(all_hard), sum(dedup_hard)/len(dedup_hard))
    print('hard100 avg len', sum(hard100)/len(hard100), sum(dedup_hard100)/len(dedup_hard100))
    print('good_click avg len', sum(all_good_click)/len(all_good_click), sum(dedup_good_click)/len(dedup_good_click))
    # # 统计出现超过3次的good_show_soft_commodity_id
    # counter = Counter(commodity_ids)
    # frequent_ids = {id: count for id, count in counter.items() if count > 3}
    # print(f'总共{total_count}行')

    return non_zero_merchant_ids

if __name__ == "__main__":
    # file_path = "短带取训练数据_Snippet 1_23817459.csv"  # 替换为你的CSV文件路径
    file_path = "/Users/<USER>/Downloads/短带取训练数据_Snippet 1_23840990.csv"
    non_zero_ids = process_csv(file_path)
    
    # print(f"找到 {len(non_zero_ids)} 个不为0的merchant_product_id")
    # print(f"good_show_soft_commodity_id不为空的比例: {empty_ratio:.2%}")
    # print(f"出现超过3次的good_show_soft_commodity_id:")
    # for id, count in sorted(frequent_ids.items(), key=lambda x: x[1], reverse=True):
    #     print(f"  ID: {id}, 出现次数: {count}")

    # # 将不为0的merchant_product_id保存到文件
    # with open("non_zero_merchant_ids.txt", "w", encoding="utf-8") as f:
    #     for id in non_zero_ids:
    #         f.write(f"{id},\n")