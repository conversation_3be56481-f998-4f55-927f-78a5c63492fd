import os
import json
import time
import jsonlines

def get_good_info(itemid:str):
    dirname = os.path.dirname(os.path.abspath(__file__))
    with open(f'{dirname}/curl_lego.sh', 'r') as f:
        content = f.read()

    content = content.replace('22939220542781', itemid) # 两处itemid
    
    # print(content)

    # curl 命令执行，获取curl返回结果
    output = os.popen(content).read()
    output = json.loads(output)['data']
    # with jsonlines.open(f'{dirname}/log_lego.jsonl','a') as f:
    #     f.write({"itemid":itemid, "time":time.asctime(), "response": output})
    # data = json.loads(output)
    data = output
    # 获取categoryProp
    try:
        prop = dict()
        for i in data['categoryProp']:
            prop[i['propName']] = i['propValue']
        images = data['imageUrls']
        price = data['itemPrice']
        title = data['title'] # 商品详情页标题有一些型号数字，不如details字段干净
        volume = data['volume']
        daysVolume = data['daysVolume']
        weburl = f'https://kwaishop-product-c.corp.kuaishou.com/web/kwaishop-goods-detail-page-app?id={itemid}&miniH5=1&source=goods360_pc_lego'
        result = {
                "itemid":itemid,
                "title":title,
                "time":time.asctime(),
                "prop":prop,
                "images":images,
                "price":price,
                "volume":volume,
                "daysVolume":daysVolume,
                "weburl":weburl
            }
        with jsonlines.open(f'{dirname}/data_lego.jsonl', 'a') as f:
            f.write(result)
        # print(result)
    except KeyError:
        print(f'[lego_api_ERROR] no results: {itemid}')

    return result


if __name__ == "__main__":
    itemid = '23120017188993'
    get_good_info(itemid)
