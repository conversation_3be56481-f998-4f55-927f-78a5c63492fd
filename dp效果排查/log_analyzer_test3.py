import re
import json
import pandas as pd
from lego_api import get_good_info
def parse_log_file(log_path):
    """解析日志文件，提取需要的列表数据，支持多个lua_result部分"""
    with open(log_path, 'r', encoding='utf-8') as f:
        log_content = f.read()

    # 查找所有lua_result部分
    lua_result_pattern = r'log_tag: lua_result[\s\S]*?(?=log_tag:|$)'
    lua_results = re.findall(lua_result_pattern, log_content, re.DOTALL)

    if not lua_results:
        print("未找到任何lua_result部分")
        return [], [], [], []

    print(f"找到 {len(lua_results)} 个lua_result部分")

    final_result = []
    for i, remap_content in enumerate(lua_results):
        print(f"\n处理第 {i+1} 个lua_result部分:")

        item_attr_pattern = r'\[item_attr\][\s\S]*?total item num: \d+[\s\S]*?\{(.*?)\}'
        item_attr_match = re.search(item_attr_pattern, remap_content, re.DOTALL)

        if not item_attr_match:
            print(f"  未找到item_attr部分")
            continue

        item_attr_content = item_attr_match.group(1)

        # 从item_attr中提取其他数据
        good_id = r'goods_item_id \(int\): (\d+)'
        good_match = re.search(good_id, item_attr_content, re.DOTALL)

        selected_pattern = r'selected_ids \(int_list\[(\d+)\]\): \[(.*?)\]'
        selected_match = re.search(selected_pattern, item_attr_content, re.DOTALL)

        indices_pattern = r'topk_indices \(int_list\[(\d+)\]\): \[(.*?)\]'
        indices_match = re.search(indices_pattern, item_attr_content, re.DOTALL)

        values_pattern = r'topk_values \(double_list\[(\d+)\]\): \[(.*?)\]'
        values_match = re.search(values_pattern, item_attr_content, re.DOTALL)

        if not good_match or not selected_match:
            print(f"  第 {i+1} 个lua_result部分缺少必要数据")
            print(f"    good_match: {bool(good_match)}")
            print(f"    selected_match: {bool(selected_match)}")
            print(f"    indices_match: {bool(indices_match)}")
            print(f"    values_match: {bool(values_match)}")
            continue

        # 解析列表数据
        try:
            selected_list = [int(x.strip()) for x in selected_match.group(2).split(',')]
            commodity_list = int(good_match.group(1))

            # 只有在匹配到的情况下才解析indices和values
            indices_list = []
            values_list = []
            if indices_match:
                indices_list = [int(x.strip()) for x in indices_match.group(2).split(',')]
            if values_match:
                values_list = [float(x.strip()) for x in values_match.group(2).split(',')]

            print(f"  成功解析第 {i+1} 个lua_result的数据:")
            print(f"    - selected_ids: {len(selected_list)}个元素")
            print(f"    - goods_item_id: {commodity_list}")
            print(f"    - topk_indices: {len(indices_list)}个元素")
            print(f"    - topk_values: {len(values_list)}个元素")

            d = {}
            distance = {}
            for idx, item_id in enumerate(selected_list):
                if item_id in d:
                    d[item_id].append(idx)
                    distance[item_id].append(values_list[idx])
                else:
                    d[item_id] = [idx]
                    distance[item_id] = [values_list[idx]]

            with open('result.log','a') as f:
                f.write(f"=== lua_result {i+1} ===\n")
                f.write(f"goods_item_id: {commodity_list}\n")
                f.write(f"dp_result: {json.dumps(d, indent=4)}\n\n")
            
            info = {}
            for k, v in d.items():
                if k not in info:
                    info[k] = get_good_info(str(k))
                url = f'https://lego.corp.kuaishou.com/page/fangzhou/goods/360search?itemId={k}'
                # final_result.append([commodity_list, k, ','.join(map(str, v)), url, distance[k]])
                final_result.append([commodity_list, k, ','.join(map(str, v)), url, info[k]['title'], int(info[k]['price'])/100, info[k]['images'][0], info[k]['prop']])
            final_result.append([''] * 3)
            pd.DataFrame(final_result).to_csv('result2.csv', index=False, header=False)


        except Exception as e:
            print(f"  解析第 {i+1} 个lua_result的列表数据时出错: {e}")
            continue


if __name__ == "__main__":
    log_path = "playground/test3.log"

    # 解析日志文件
    parse_log_file(log_path)
