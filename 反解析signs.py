import numpy as np

def main(n, sign_format):
    n = np.int64(n)
    sign = np.uint64(n)
    # print(n,sign)

    sign = int(sign)
    if sign_format == 'mio':
        if sign >> 60 != 0:
            return sign, sign >> 60
        else:
            return sign, 16 + (sign >> 48)
    elif sign_format == 'kuiba':
        # 低50位掩码
        mask = (1 << 54) - 1
        return sign, sign >> 54
    else:
        # klearner
        return sign, sign >> 52

def offset(signs, base_sign=15852694747139552520, base_id=4341494099580564744):
    res = []
    for sign in signs:
        delta = sign - base_sign
        id = base_id + delta
        res.append(id)
    return res

if __name__ == '__main__':
    # 用已确定的共有key的sign值，计算id
    uintsigns = [15852692186069976000,15852694508489586540,15852694332433389060,15852691765954581780,15852691982559105030]
    id = offset(uintsigns)
    print(id)

    sign_format = 'kuiba'
    # text = """[-2594073385365105950, -2594073385365105951, -2594073385365105952, -2594073385365105953, -2594073385365105954, -2594073385365105955, -2594073385365105956, -2594073385365105957, -2594073385365105958, -2594073385365105959, -2594073385365105960, -2594073385365105961, -2594073385365105962, -2594073385365105963, -2594073385365105964, -2594073385365105965, -2594073385365105966, -2594073385365105967, -2594073385365105968, -2594073385365105969, -2594073385365105970, -2594073385365105971, -2594073385365105972, -2594073385365105973, -2594073385365105974, -2594073385365105975, -2594073385365105976, -2594073385365105977, -2594073385365105978, -2594073385365105979, -2594073385365105980, -2594073385365105981, -2594073385365105982, -2594073385365105983, -2594073385365105984, -2594073385365105985, -2594073385365105986, -2594073385365105987, -2594073385365105988, -2594073385365105989, -2594073385365105990, -2594073385365105991, -2594073385365105992, -2594073385365105993, -2594073385365105994, -2594073385365105995, -2594073385365105996, -2594073385365105997, -2594073385365105998, -2594073385365105999, -2594073385365106000, -2594073385365106001, -2594073385365106002, -2594073385365106003, -2594073385365106004, -2594073385365106005, -2594073385365106006, -2594073385365106007, -2594073385365106008, -2594073385365106009, -2594073385365106010, -2594073385365106011, -2594073385365106012, -2594073385365106013, -2594073385365106014, -2594073385365106015, -2594073385365106016, -2594073385365106017, -2594073385365106018, -2594073385365106019, -2594073385365106020, -2594073385365106021, -2594073385365106022, -2594073385365106023, -2594073385365106024, -2594073385365106025, -2594073385365106026, -2594073385365106027, -2594073385365106028, -2594073385365106029, -2594073385365106030, -2594073385365106031, -2594073385365106032, -2594073385365106033, -2594073385365106034, -2594073385365106035, -2594073385365106036, -2594073385365106037, -2594073385365106038, -2594073385365106039, -2594073385365106040, -2594073385365106041, -2594073385365106042, -2594073385365106043, -2594073385365106044, -2594073385365106045, -2594073385365106046, -2594073385365106047, -2594073385365106048, -2594073385365106049]"""
    # text = """[-2594049326569999071, -2594049326569999072, -2594049326569999073, -2594049326569999074, -2594049326569999075, -2594049326569999076, -2594049326569999077, -2594049326569999078, -2594049326569999079, -2594049326569999080, -2594049326569999081, -2594049326569999082, -2594049326569999083, -2594049326569999084, -2594049326569999085, -2594049326569999086, -2594049326569999087, -2594049326569999088, -2594049326569999089, -2594049326569999090, -2594049326569999091, -2594049326569999092, -2594049326569999093, -2594049326569999094, -2594049326569999095, -2594049326569999096, -2594049326569999097, -2594049326569999098, -2594049326569999099, -2594049326569999100]"""
    # text = """[-2594049326569999096]"""
    # 这里text是上述id取kuiba signs的int64结果，去验证这些int64是否对应开头的uint64
    text = """[-2594052091150446586, -2594052307754969836, -2594049741276162556, -2594049565219965076, -2594051887639575616]"""
    l = eval(text)
    final_id = []
    for n in l:
        sign, slot = main(n, sign_format)
        if slot == 880 and sign in uintsigns:
            final_id.append(id[uintsigns.index(sign)])
            print(f'curl "localhost:$THEPORT/get_batch_embeddings?signs={sign}&sign_format=kuiba"')
    print(final_id)

    
