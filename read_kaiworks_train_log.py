import json
x = """
stdout auc: AUC=0.860081 UAUC=0.636190 WUAUC=0.631954 UserNum=2684 AllUserNum=1842879 InsNum=3422401 MAE=0.002854 RMSE=0.037688 LOSS=0.009124 ActualCTR=0.001449 PredictedCTR=0.001462
stdout rocket_auc: AUC=0.848465 UAUC=0.635890 WUAUC=0.631807 UserNum=2684 AllUserNum=1842879 InsNum=3422401 MAE=0.003796 RMSE=0.037735 LOSS=0.009491 ActualCTR=0.001449 PredictedCTR=0.002408
stdout ue_auc: AUC=0.824829 UAUC=0.640611 WUAUC=0.629644 UserNum=244 AllUserNum=234562 InsNum=389403 MAE=0.002440 RMSE=0.034613 LOSS=0.008283 ActualCTR=0.001204 PredictedCTR=0.001252
stdout ue_rocket_auc: AUC=0.827980 UAUC=0.652132 WUAUC=0.643158 UserNum=244 AllUserNum=234562 InsNum=389403 MAE=0.002486 RMSE=0.034612 LOSS=0.008258 ActualCTR=0.001204 PredictedCTR=0.001299
stdout auc_global: AUC=0.871464 UAUC=0.663861 WUAUC=0.652291 UserNum=2215 AllUserNum=1842879 InsNum=3422401 MAE=0.002241 RMSE=0.033381 LOSS=0.007342 ActualCTR=0.001133 PredictedCTR=0.001144
stdout auc_direct: AUC=0.866641 UAUC=0.646761 WUAUC=0.638082 UserNum=2548 AllUserNum=1842879 InsNum=3422401 MAE=0.002608 RMSE=0.036030 LOSS=0.008396 ActualCTR=0.001324 PredictedCTR=0.001334
stdout soft_auc: AUC=0.761302 UAUC=0.565635 WUAUC=0.560601 UserNum=2684 AllUserNum=1842879 InsNum=3422401 MAE=0.006229 RMSE=0.039116 LOSS=0.012050 ActualCTR=0.001449 PredictedCTR=0.004859
stdout reco_auc: AUC=0.877166 UAUC=0.642880 WUAUC=0.648289 UserNum=103 AllUserNum=368328 InsNum=417599 MAE=0.002893 RMSE=0.038284 LOSS=0.009306 ActualCTR=0.001482 PredictedCTR=0.001443
stdout final_gmv_order: AUC=0.707655 UAUC=0.707655 WUAUC=0.699018 UserNum=535 AllUserNum=1128892 Size=1637156 InsPerUser=2.672897 MAE=0.001381 MAPE=0.999307 APE_P10=0.000000 RMSE=0.026181 ActualWatch=0.000706 PredWatch=0.000694 STRICT_UAUC=0.707655
stdout final_gmv_roas: AUC=0.714527 UAUC=0.714527 WUAUC=0.707328 UserNum=761 AllUserNum=1169794 Size=1785245 InsPerUser=2.840999 MAE=0.001725 MAPE=0.999118 APE_P10=0.000000 RMSE=0.029056 ActualWatch=0.000871 PredWatch=0.000883 STRICT_UAUC=0.714527
stdout 2025-06-05 10:58:02: stability_controller.py:386 当前要求必须计算并上报的“关键训练目标”为: ['reco_auc', 'auc'], 假如未输出完整的关键训练目标则操作是: 任务继续训练，但不导出模型
"""

time = ''
all_metrics = dict()
x = x.strip().splitlines()
for i in x:
    if 'stability_controller.py' in i:
        time = i.split(': stability_controller.py')[0].removeprefix('stdout ')
    else:
        name, metrics = i.split(':', 1)
        name = name.removeprefix('stdout ')
        metrics = metrics.strip().split(' ')
        all_metrics[name] = dict()
        for metric in metrics:
            k,v = metric.split('=')
            all_metrics[name][k] = float(v)

print(json.dumps(all_metrics, indent=4, ensure_ascii=False))

print('**************')
print(time)
print(f"auc: {all_metrics['auc']['AUC']:.6f}")
print(f"ue_auc: {all_metrics['ue_auc']['AUC']:.6f}")
print(f"ue_rocket_improve: {all_metrics['ue_rocket_auc']['AUC'] - all_metrics['ue_auc']['AUC']:.6f}")