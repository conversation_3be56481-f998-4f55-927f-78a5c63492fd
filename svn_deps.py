txt = """
2025-04-14 18:26:15,388 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 mmu/common 失败. 错误类型: PERMISSION_DENIED: **可能无目录 mmu 的权限**.
2025-04-14 18:26:15,388 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/mmu. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,388 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 teams/reco_follow 失败. 错误类型: PERMISSION_DENIED: **可能无目录 teams/reco_follow 的权限**.
2025-04-14 18:26:15,388 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/teams/reco_follow. 若无权限的, 参
考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,388 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 teams/aiplatform/knn 失败. 错误类型: PERMISSION_DENIED: **可能无目录 teams/aiplatform 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/teams/aiplatform. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/BladeDisc 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/BladeDisc 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/BladeDisc. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/faiss-1.7.3 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/faiss-1.7.3 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/faiss-1.7.3. 若无权限
的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/scann_1.2.9 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/scann_1.2.9 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/scann_1.2.9. 若无权限
的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/tensorflow-gpu 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/tensorflow-gpu 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-gpu. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/tensorflow-2.4.1 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/tensorflow-2.4.1 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-2.4.1. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 third_party/tensorflow-2.10.0 失败. 错误类型: PERMISSION_DENIED: **可能无目录 third_party/tensorflow-2.10.0 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-2.10.0. 若
无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请.
2025-04-14 18:26:15,389 [ERROR] [dump_get_src_error] [src_manager.py: 229] 更新目录 teams/aiplatform/cofea_convert 失败. 错误类型: PERMISSION_DENIED: **可能无目录 teams/aiplatform 的权限**.
2025-04-14 18:26:15,389 [INFO] [dump_get_src_error] [src_manager.py: 232] 参考解决方案: 首先确认是否拥有权限, 尝试执行 svn list https://devsvn.corp.kuaishou.com:2014/codebase/ks/teams/aiplatform. 若无权限的, 参考链接 https://docs.corp.kuaishou.com/k/home/<USER>/fcAB6inPnL8Chb7CYCHy7U6Za#section=h.mtpcdm10mn2o 进行申请."""

for line in txt.split('\n'):
    if 'svn list' in line:
        cmd = line.split('svn list')[1].split('. 若')[0].strip()
        print(f"{cmd}")
