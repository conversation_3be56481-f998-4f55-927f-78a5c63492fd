text = """
add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PCTR_LIST, ItemIdx::reco_live_fr_pctr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PSVR_LIST, ItemIdx::reco_live_fr_psvr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PLVTR_LIST, ItemIdx::reco_live_fr_plvtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PLTR_LIST, ItemIdx::reco_live_fr_pltr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PWTR_LIST, ItemIdx::reco_live_fr_pwtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PCMTR_LIST, ItemIdx::reco_live_fr_pcmtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PHTR_LIST, ItemIdx::reco_live_fr_phtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_IN_LVTR_LIST, ItemIdx::reco_live_fr_in_lvtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_IN_ETR_LIST, ItemIdx::reco_live_fr_in_etr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_ETR_LIST, ItemIdx::reco_live_fr_etr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_CTETR_LIST, ItemIdx::reco_live_fr_ctetr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_CTLVTR_LIST, ItemIdx::reco_live_fr_ctlvtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_FANSTR_LIST, ItemIdx::reco_live_fr_fanstr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_GTR_LIST, ItemIdx::reco_live_fr_gtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_NEGTR_LIST, ItemIdx::reco_live_fr_negtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PPTR_LIST, ItemIdx::reco_live_fr_pptr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_REPORTR_LIST, ItemIdx::reco_live_fr_reportr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_SHTR_LIST, ItemIdx::reco_live_fr_shtr);  // NOLINT
      add_live_uescore(p_ad, ContextInfoCommonAttr::FRRANK_UESCORE_LIVE_PVTR_LIST, ItemIdx::reco_live_fr_pvtr);  // NOLINT
      """.strip().splitlines()

fields = []
for s in text:
    a = s.split('UESCORE_LIVE_')[-1].split('_LIST')
    live_name = a[0]
    rest = a[1]
    rest = rest.split('reco_live_fr_')[-1].split(')')[0]

    if not live_name == rest.upper():
        print(live_name, rest)
    else:
        fields.append(rest)
 
print(fields)