import datetime

d1 = datetime.datetime(2022,11,10)   # 第一个日期
d2 = datetime.datetime(2025,8,7)   # 第二个日期
interval = d2 - d1                   # 两日期差距
print(interval.days)                        # 具体的天数                     

time1 = datetime.datetime.now() - datetime.timedelta(hours = 4)#(days=2)
time2 = datetime.datetime.now()
time1 = int(time1.timestamp() * 1000)
time2 = int(time2.timestamp() * 1000)

print(f"\n&from={time1}&to={time2}\n")
