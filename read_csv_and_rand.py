import pandas as pd
import datetime
import tkinter as tk
from tkinter import filedialog
def select_files():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    file_paths = filedialog.askopenfilenames(initialdir='/Users/<USER>/Downloads/',defaultextension='csv')
    print("选择的文件路径:", file_paths)
    return file_paths

# 如果有多个文件，就拼接且最后混合
files = select_files()
df_all = []
for file in files:
    df_all.append(pd.read_csv(file))


target_column = 'photo_id'
sample_n = 100
sampled_df = pd.concat(df_all).sample(n = sample_n, random_state=42)

final_dict = sampled_df.to_dict(orient='list')

now_str = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")
writeToFile = f'{target_column}.{now_str}.log'
with open(writeToFile,'w') as f:
    f.write(str(final_dict[target_column]))
print('write to ',writeToFile)