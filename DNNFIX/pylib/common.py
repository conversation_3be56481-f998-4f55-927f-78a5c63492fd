import numpy as np
import pandas as pd
from pandas import Series,DataFrame
import datetime,sys
import subprocess,logging

# import myutil

logging.basicConfig(stream=sys.stderr, level=logging.INFO, format='%(asctime)s '+'%(levelname)s - %(message)s')
log=logging.getLogger()
log.setLevel(logging.INFO)


def check_eq(a,b, msg=''):
    if a!=b:
        raise Exception("{} mismatch {} vs {}".format(msg, a, b))
def check_lt(a,b, msg=''):
    if not a<b:
        raise Exception("{} check lt failed {} vs {}".format(msg, a, b))
def run_shell(cmd,check_ret=True, cwd='.'):
    log.info("run shell: [ %s ], cwd=%s", cmd, cwd)
    ret = subprocess.check_output(cmd, shell=True, encoding='utf-8' ,cwd=cwd)
#     print (ret)
    return ret

def get_shell_out(cmd, cwd='.', stream=True, silent=False):
    if not silent:
        log.info("run shell: [ %s ], cwd=%s", cmd, cwd)
    ret = subprocess.Popen(cmd, shell=True, encoding='utf-8' ,cwd=cwd, stdout=subprocess.PIPE)
    if not stream:
        return ret.stdout.read()
    return ret.stdout

def get_shell_in(cmd,check_ret=True, cwd='.'):
    log.info("run shell: [ %s ], cwd=%s", cmd, cwd)
    ret = subprocess.Popen(cmd, shell=True, encoding='utf-8' ,cwd=cwd, stdin=subprocess.PIPE)
    return ret.stdin
# a=get_shell_out('ssh  mpi@bjfk-rs10792.yz02 cat /home/<USER>/sunchenggen/auto_feature/acfun_lr/out.log')

def remote_read(host, file):
    return get_shell_out('ssh %s  "cat  %s"'%(host, file))

def remote_write(host, file):
    return get_shell_in('ssh %s  "cat - > %s"'%(host, file))

def remote_shell_read(host, cmd):
    return get_shell_out('ssh %s  "%s"'%(host, cmd))

def remote_shell_write(host, cmd):
    return get_shell_in('ssh %s  "%s"'%(host, cmd))

def is_hdfs(file):
    return file.startswith('viewfs') or file.startswith('hdfs')
class RemoteRunner:
    
    def __init__(self, host=None, cwd='.'):
        self.host=host
        self.cwd=cwd
        try:
            self.read_cmd('hostname', stream=False)
        except:
            raise Exception(f"连接{self.host}失败， 第一次连的机器需要在 File->New->Terminal里面手动ssh连一下")
    
    def wrap_cmd(self, cmd):
        if self.host:
            cmd='ssh %s  "cd %s && %s"'%(self.host, self.cwd, cmd)
        else:
            cmd="cd %s && %s"%(self.cwd, cmd)
        return cmd
        
    def read_cmd(self, cmd, stream=True, encoding='utf-8'):
        cmd=self.wrap_cmd(cmd)
        log.info("run shell: [ %s ]", cmd)
        ret = subprocess.Popen(cmd, shell=True, encoding=encoding , stdout=subprocess.PIPE)
        if not stream:
            rs=ret.stdout.read()
            return_code = ret.wait()
            if return_code:
                raise subprocess.CalledProcessError(return_code, cmd)
            return rs
            
        return ret.stdout


    def write_cmd(self, cmd, content=None):
        cmd=self.wrap_cmd(cmd)
        ret = get_shell_in(cmd)
        if content is not None:
            ret.write(content)
            ret.close()
            return None
        return ret
        
    
    def read_write_cmd(self, cmd):
        cmd=self.wrap_cmd(cmd)
        log.info("run shell: [ %s ]", cmd)
        ret = subprocess.Popen(cmd, shell=True, encoding='utf-8' , stdout=subprocess.PIPE,stdin=subprocess.PIPE)
        return ret
    

    
    def read_file(self, file, stream=True, encoding='utf-8'):
        if is_hdfs(file):
            return self.read_cmd('hadoop fs -text %s'%file, stream, encoding)
        return self.read_cmd('cat %s'%file, stream, encoding)
    
    def write_file(self, file, content=None):
        if is_hdfs(file):
            return self.write_cmd('hadoop fs -put - %s'%file, content)
        return self.write_cmd('cat - >%s'%file, content)
    
class LocalRunner(RemoteRunner):
    
    def __init__(self, cwd='.'):
        RemoteRunner.__init__(self, cwd=cwd)
    
    
def jup_set_show_all():
    from IPython.core.interactiveshell import InteractiveShell
    InteractiveShell.ast_node_interactivity = "all"
    
