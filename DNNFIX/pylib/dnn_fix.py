from pylib.common import *
import os, sys, math
import yaml


class DnnParam:
    def __init__(self, host=None, cwd='.'):
        self.remote = RemoteRunner(host, cwd)
        pass

    def read_done_list(self, done_list):
        done_list = self.remote.read_file(done_list, stream=False).split('\n')[-50:]
        done_list = [x.split('\t') for x in done_list]
        text_model = [x for x in done_list if len(x) > 3]
        return text_model

    def write_done_list(self, done_list, path):
        txt = '\n'.join(['\t'.join(x) for x in done_list])
        if 'viewfs' in path or 'hdfs' in path:
            try:
                self.remote.read_cmd('hadoop fs -mv {} {} ; hadoop fs -rm -r {}'.format(path, path + '.old', path),
                                     stream=False)
            except:
                pass
                # print('error')
        print('\t'.join(done_list[-1]))
        fp = self.remote.write_file(path)
        for row in done_list:
            fp.write('\t'.join(row) + '\n')
        fp.close()

    def get_dnn_model_path(self, done_list):
        done_list = self.remote.read_file(done_list, stream=False).split('\n')[-50:]
        done_list = [x.split('\t') for x in done_list]
        text_model = [x for x in done_list if len(x) > 3][-1]
        model_path = text_model[2]
        dnn_path = model_path
        return dnn_path

    def load_param_conf(self, dnn_plugin):
        with self.remote.read_file(dnn_plugin) as f:
            conf = yaml.safe_load(f)
        embs = {x['input_name']: x for x in conf['embedding']['slots_config']}
        params = {x['name']: x for x in conf['param']}

        env_str = ''
        out = {}
        out_sym = {}
        out['bias_input'] = 1
        out_sym['bias_input'] = '1'
        for k, v in embs.items():
            dim = int(v['dim'])
            expand = int(v.get('expand', '1'))
            slots = str(v['slots']).split()
            n = len(slots)
            total_dim = expand * dim * n
            if v.get('use_sum'):
                total_dim = dim
            env_str += '{}_size={} '.format(k, total_dim)
            out[k] = total_dim
            out_sym[k] = '$' + k + '_size'

        def calc(s):
            return int(get_shell_out('{}; echo -n {}'.format(env_str, s), silent=True).read())

        if 'summary' in conf and conf['summary']:
            for x in conf['summary']:
                x['len'] = calc(x['len'])
        tot_len = 0
        for k, v in params.items():
            v['rown'] = calc(v['rown'])
            v['coln'] = calc(v['coln'])
            tot_len += v['rown'] * v['coln']
        dnn_param = {k: [params[k]['rown'], params[k]['coln']] for k in params}
        return dnn_param

    def fs_exists(self, path):
        try:
            self.remote.read_cmd('hadoop fs -test -e %s' % (path), stream=False)
            return True
        except:
            return False
        
    def nomalize_shape(self, shape):
        s=shape[:]
        while len(s)< 2:
            s.append(1)
        if len(s)>2:
            rows = s[0]
            cols = 1
            for v in s[1:]:
                cols*=v
            s=[rows, cols]
        return s
        
    def load_general_text(self, table_file):
        model = []
        self.params_size = {}
        for line in self.remote.read_file(table_file):
            fields = line.split()
            name = fields[1]
        
            shape = [int(x) for x in  fields[4][1:-1].split(',')]
            weight = [float(x) for x in fields[5].split(',')]
            extra = [float(x) for x in fields[8].split(',')]
            extra_size = int(fields[7])
            opt_type= fields[6]
            
            shape = self.nomalize_shape(shape)
            if name.endswith(":0"):
                name=name[:-2]
            self.params_size[name] = shape[0]*shape[1]
            check_eq(shape[0]*shape[1], len(weight), msg="shape mismatch")
            v=np.zeros(shape=[len(weight), 5])
            v[:, 0]=weight
#             check_eq(len(extra), extra_size, "extra size error")
            extra_per_weight = len(extra)//len(weight)
            constant_extra= len(extra)-extra_per_weight*len(weight)
            check_lt(extra_per_weight+constant_extra, 5, "extra size <5 ")
            v[:, 1:extra_per_weight+1]=np.array(extra[:extra_per_weight*len(weight)]).reshape([extra_per_weight, len(weight)]).transpose([1, 0])
            v[:, extra_per_weight+1:extra_per_weight+1+constant_extra]=np.array(extra[extra_per_weight*len(weight):])
            model.append(v)
            print(name, opt_type, len(weight), extra_per_weight, constant_extra)
        return np.concatenate(model)
         
            

    def load_model(self, done_file=None, model_path=None):
        if done_file:
            model_path = self.get_dnn_model_path(done_file)
        print('model_path:', model_path)
        assert model_path

        model = []
        self.format = "mio_colmajor"
        if self.fs_exists(model_path + '/meta.json'):
            with self.remote.read_file(model_path + '/meta.json') as f:
                import json
                meta = json.load(f)
                self.format = meta['dense']['format']
                params = meta['dense']['params']
                self.params_conf = {p["name"]: p['shape'] for p in params}
                for k,v in  self.params_conf.items():
                    if len(v)<2:
                        v.append(1)
            model_path += '/dense'
        elif self.fs_exists(model_path + '/dense/dnn_meta.json'):
            self.format='general_text'
            model_path += '/dense'
        else:
            model_path += '/dnn_plugin'
        file = model_path + '/table'

        if self.fs_exists(model_path + '/table.old'):
            file = model_path + '/table.old'
        if self.format=='general_text':
            model = self.load_general_text(file)
        else:
            for line in self.remote.read_cmd('hadoop fs -cat %s' % (file)):
                model.append([float(x) for x in line.strip().split('\t')])
            model = np.array(model)
        log.info('\033[01;32m load model from {} success \033[0m'.format(model_path))

        return model

    def split_model(self, model, params_conf):
        if hasattr(self, 'params_size'):
            params_conf = {k: params_conf[k] for k,v in self.params_size.items()}
        tot_len = 0
        for k, v in params_conf.items():
            tot_len += v[0] * v[1]
        check_eq(tot_len, model.shape[0])
        rs = {}
        oft = 0
        need_trans = self.format == "mio_colmajor"
        print(f'transpose={need_trans}')
        for k, v in params_conf.items():
            n = v[0] * v[1]
            p = model[oft:oft + n, :]
            if need_trans:
                p = p.reshape([v[1], v[0], 5]).transpose(1, 0, 2)
            else:
                p = p.reshape([v[0], v[1], 5])
            rs[k] = p
            oft += n
        return rs
    
    def load_kai_model(self, done_file=None, model_path=None):
        model = self.load_model(done_file, model_path)
        return self.split_model(model, self.params_conf)

    def create_param(self, name, params, shape, conf, init_range=None, dry_run=False):
        new_p = None
        if not dry_run:
            new_p = np.zeros([shape[0], shape[1], 5])
        bitmap = np.zeros([shape[0], shape[1]])
        default_init_range = 0.2
        log.info('create param: ' + name)
        if isinstance(conf, dict) and ('row' in conf or 'col' in conf):
            i = 1
            dim = 'col'
            if 'row' in conf:
                i = 0
                dim = 'row'

            real_conf = []
            dst_n = 0
            src_n = 0
            fr = conf.get('from', name)
            oldp = params[fr]
            if oldp.shape[1 - i] != shape[1 - i]:
                raise Exception('{} mismatch {} vs {}'.format(dim, oldp.shape[1 - i], shape[1 - i]))
            for c in conf[dim]:
                if isinstance(c, int):
                    if i == 0:
                        real_conf.append({'to_range': [dst_n, 0, c, -1], 'from_range': [src_n, 0]})
                    else:
                        real_conf.append({'to_range': [0, dst_n, -1, c], 'from_range': [0, src_n]})

                    dst_n += c
                    src_n += c
                else:
                    assert isinstance(c, dict)
                    if 'skip' in c:
                        src_n += c['skip']
                    n = c.get('len', 0)
                    if n == 0:
                        continue
                    init = c['init']
                    if i == 0:
                        c.update({'to_range': [dst_n, 0, n, -1]})
                    else:
                        c.update({'to_range': [0, dst_n, -1, n]})
                    real_conf.append(c)
                    dst_n += n
            check_eq(dst_n, shape[i], msg=name)
            check_eq(src_n, oldp.shape[i], msg=name)
            conf = real_conf
            if dry_run:
                log.info('conf convert to: {}'.format(conf))

        if not isinstance(conf, list):
            conf = [conf]

        for c in conf:
            if isinstance(c, str):
                c = {'from': c}
            to_range = c.get('to_range', [0, 0, -1, -1])
            for i in range(2):
                if to_range[-2 + i] < 0:
                    to_range[-2 + i] = shape[i] - to_range[i]
            if 'from' not in c:
                c['from'] = name
            if 'init' in c:
                p = np.zeros([to_range[2], to_range[3], 5])

                if c['init'] == 'kaiming':
                    scale = c.get('init_range', init_range if init_range else default_init_range)
                    r = scale * math.sqrt(1 / shape[0])
                    p[:, :, 0] = np.random.default_rng().normal(0, r, [to_range[2], to_range[3]])
                elif c['init'] == 'constant':
                    log.info('init constant' + c['constant'])
                    p[:, :, 0] = float(c['constant'])
            elif 'from' in c:
                if c['from'] not in params:
                    raise Exception("{} not in params".format(c['from']))
                p = params[c['from']]
                from_range = c.get('from_range', [0, 0])
                for i in range(2):
                    #                     if p.shape[i]-from_range[i]<to_range[-2+i]:
                    #                         log.warning("shape {} mismatch from: {}:{} to: {}:{} ". format(i, c['from'], p.shape[i]-from_range[i], name,  to_range[-2+i] ))
                    to_range[-2 + i] = min(p.shape[i] - from_range[i], to_range[-2 + i])
                p = p[from_range[0]:from_range[0] + to_range[2], from_range[1]:from_range[1] + to_range[3], :]

            if not dry_run:
                new_p[to_range[0]:to_range[0] + to_range[2], to_range[1]:to_range[1] + to_range[3], :] = p
            bitmap[to_range[0]:to_range[0] + to_range[2], to_range[1]:to_range[1] + to_range[3]] += 1

        for r in range(shape[0]):
            for c in range(shape[1]):
                if bitmap[r, c] != 1:
                    raise Exception(
                        'param {} row={}/{}, col={}/{} assign {} times'.format(name, r, shape[0], c, shape[1],
                                                                               bitmap[r, c]))
        return new_p

    def update_model(self, params, new_conf, mapping=None, init_range=None):
        '''转换模型, mapping是要修改的参数关系，不修改的参数不用配
        mapping={
        'new_param1': 'old_param',//直接复制
        'new_param2': [ //old_param的[from_r, from_c]位置复制到new_param2的[r,c]位置，复制的大小为[m,n]，m,n为-1代表复制到最后
                        {from: 'old_param', 'to_range': [r,c, m,n], from_range:[from_r, from_c] }, 
                        //new_param2的[r,c]位置，大小为[m,n]，用0初始化
                        {init: 'zero', to_range: [r,c, m,n] },
                        //new_param2的[r,c]位置，大小为[m,n]，用kaiming初始化，init_range为range
                        {init: 'kaiming', to_range: [r,c, m,n], 'init_range': range },
                        ]
        
        }
        
         '''
        tot_len = 0
        for k, v in new_conf.items():
            tot_len += v[0] * v[1]
        rs = {}
        oft = 0
        if not mapping:
            mapping = {}
        for k, v in new_conf.items():
            names = k
            if k in mapping:
                names = mapping[k]
            self.create_param(k, params, v, names, init_range=init_range, dry_run=True)
        for k, v in new_conf.items():
            names = k
            if k in mapping:
                names = mapping[k]
            rs[k] = self.create_param(k, params, v, names, init_range=init_range)
        return rs

    def diff(self, params, new_conf, mapping=None, show_remove=False):
        '''
        mapping={
        'new_param1': 'old_param',
        
        }
        '''
        tot_len = 0
        for k, v in new_conf.items():
            tot_len += v[0] * v[1]
        rs = {}
        oft = 0
        diffs = []
        if not mapping:
            mapping = {}

        used = set()
        for k, v in new_conf.items():
            name = k
            if k in mapping:
                name = mapping[k]
            d = {'name': k, 'old_name': name, 'new_shape': v}
            if name not in params:
                d['type'] = 'add'
                diffs.append(d)
            else:
                used.add(name)
                old_shape = params[name].shape
                d.update({'old_shape': [old_shape[0], old_shape[1]], 'type': 'change',
                          'diff': [v[0] - old_shape[0], v[1] - old_shape[1]]})
                if v[0] != old_shape[0] or v[1] != old_shape[1]:
                    diffs.append(d)

        if show_remove:
            for k, v in params.items():
                if k not in used:
                    d = {'old_name': k, 'type': 'remove', 'old_shape': [v.shape[0], v.shape[1]]}
                    diffs.append(d)
        return pd.DataFrame(diffs)

    def merge_model(self, params):
        tot_len = 0
        for k, v in params.items():
            tot_len += v.shape[0] * v.shape[1]
        model = np.zeros([tot_len, 5])
        oft = 0
        for k, v in params.items():
            n = v.shape[0] * v.shape[1]
            p = v
            if self.format == 'mio_colmajor':
                p = p.transpose(1, 0, 2)
            model[oft:oft + n, :] = p.reshape([n, 5])
            oft += n
        return model

    def change_done_list(self, old_done_list, new_done_list, model_path, new_dnn_path='dnn_plugin_new'):

        done_list = self.read_done_list(old_done_list)
        done_list.append(done_list[-1][:])
        paths = done_list[-1][-1].split(',')
        if len(paths) == 2:
            done_list[-1][-1] = paths[0] + ',/' + new_dnn_path
        else:
            done_list[-1][-1] = {'0': '5', '2': '4', '5': '5', '4': '4', }[done_list[-1][-1]]
        check_eq(done_list[-1][2], model_path, msg='model_path mismatch done_list')
        fmt = done_list[-1][-2].split(',')
        if fmt[1]=='general_text':
            fmt[1]='kai_dense'
            done_list[-1][-2]=fmt[0] + ',' + fmt[1]
        self.write_done_list(done_list, new_done_list)
        return

    def save_model(self, model, model_path, old_done_list, new_done_list, new_dnn_path=None, fp=None):
        import time
        remote = self.remote
        if new_dnn_path is None:
            if self.format == 'mio_colmajor':
                new_dnn_path = 'dnn_plugin_new'
            else:
                new_dnn_path = 'dense_new_' + time.strftime('%Y%m%d_%H%M%S', time.localtime())
        path = model_path + '/' + new_dnn_path
        if isinstance(model, dict):
            model = self.merge_model(model)
        if not fp:
            if 'viewfs' in path or 'hdfs' in path:
                try:
                    remote.read_cmd('export HADOOP_USER_NAME=ad', stream=False)
                    remote.read_cmd('hadoop fs -mkdir -p {} '.format(path), stream=False)
                    if self.format == 'mio_colmajor':
                        remote.read_cmd('hadoop fs -cp -f {} {} '.format(model_path + '/dnn_plugin/summary_table',
                                                                         f'{model_path}/{new_dnn_path}/summary_table'),
                                        stream=False)
                    remote.read_cmd('hadoop fs -mv {} {}'.format(path + '/table', path + '/table.old'), stream=False)
                    remote.read_cmd('hadoop fs -rm -r {}'.format(path + '/table'), stream=False)
                except:
                    pass
                fp = remote.write_cmd('hadoop fs -put - %s' % (path + '/table'))
            else:
                remote.read_cmd('mkdir -p %s' % path, stream=False)
                fp = remote.write_file(path + '/table')
        #         with remote.write_cmd('hadoop fs -put - %s'%(path+'/table')) as f:
        for row in model:
            fp.write('\t'.join([str(x) for x in row]) + '\n')
        fp.close()
        if self.format == 'mio_colmajor':
            remote.write_file(path + '/summary_table')
        self.change_done_list(old_done_list, new_done_list, model_path, new_dnn_path=new_dnn_path)
        log.info('\033[01;32m save model to {} success \033[0m'.format(path))
        return
