import os
import pandas as pd
import yaml
import numpy as np
from pylib.common import *
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
# 增加显示宽度，防止自动换行
pd.set_option('display.width', 1000)

DEFAULT_OPT_TYPE = 'Adam'


class DenseTable(object):
    def __init__(self, table_name, var_name_list, shape_list, offset_list, opt_type_list, extra_dict=None):
        self.dense_table_name = table_name
        self.var_name_list = var_name_list
        self.shape_list = shape_list
        self.offset_list = offset_list
        self.opt_type_list = opt_type_list

        assert len(var_name_list) == len(shape_list)
        assert len(var_name_list) == len(opt_type_list)

        self.extra_dict = {}
        if extra_dict is not None:
            self.extra_dict = extra_dict
#             print(extra_dict)

    def get_variable_list(self, mock_weight_value=0, mock_extra_value=0):
        weight = {}

        for idx, var_name in enumerate(self.var_name_list):
            weight[var_name] = np.full(
                self.shape_list[idx], mock_weight_value)

        extra = {}
        if len(self.extra_dict) == 0:
            for idx, var_name in enumerate(self.var_name_list):
                self.extra_dict[var_name] = np.full(
                    self.extra_dim(self.opt_type_list[idx],
                                   self.shape_list[idx]), mock_extra_value)
            extra = self.extra_dict
        else:
            extra = self.extra_dict

        return weight, extra

    def extra_dim(self, opt_type, dim):
        assert opt_type in ['Sgd', 'Momentum', 'Adam', 'AdagradV2', 'AdagradV2ShareG2Sum',
                            'AssignAdd', 'SgdL1', 'AdagradW', 'ConstDecay', 'BNMovingAverage', 'MioDense', 'Adagrad']
        total_dim = dim[0] * dim[1]
        if opt_type == 'Adam':
            return 2 * total_dim + 2
        elif opt_type == 'Momentum':
            return total_dim
        elif opt_type == 'SgdL1':
            return total_dim + 1
        elif opt_type == 'ConstDecay':
            return 1
        elif opt_type == 'AdagradV2':
            return total_dim
        elif opt_type == 'AdagradV2ShareG2Sum':
            return 1
        elif opt_type == 'AdagradW':
            return total_dim
        elif opt_type == 'MioDense':
            return 3 * total_dim
        elif opt_type == 'BNMovingAverage':
            return 0
        elif opt_type == 'AssignAdd':
            return 0
        elif opt_type == 'Adagrad':
            return 1
        else:
            print("Unknown optimizer type: {}, using {} instead".format(
                opt_type, DEFAULT_OPT_TYPE))

    def __str__(self):
        weight, extra = self.get_variable_list()
        s = "DenseTable: {} \n".format(self.dense_table_name)

        weight_data = []
        for name, var in weight.items():
            weight_data.append({"name": name, "shape": var.shape})

        extra_data = []
        for name, var in extra.items():
            opt_type = DEFAULT_OPT_TYPE
            try:
                opt_type = self.opt_type_list[self.var_name_list.index(name)]
            except Exception as e:
                print("Var: {} 's opt_type not found".format(name))
            extra_data.append(
                {"name": name, "shape": var.shape, "opt_type": opt_type})
        s += "Weight: \n"
        weight_data = pd.DataFrame(weight_data)
        s += weight_data.__str__()
        s += "\nExtra: \n"
        extra_data = pd.DataFrame(extra_data)
        s += extra_data.__str__()
        return s


class Dict2Obj(dict):
    def __getattr__(self, key):
        if key not in self:
            return None
        else:
            value = self[key]
            if isinstance(value, dict):
                value = Dict2Obj(value)
            return value


def get_dense_table_from_dnn_plugin(host, yaml_path):
    """
    从 dnn-plugin.yaml的信息中生成 DenseTable

    Args:
        yaml_path (str): 本地的 dnn-plugin.yaml 的地址

    Returns:
         DenseTable: dnn-plugin.yaml 对应的模型的 Dense 参数信息
    """
    remote = RemoteRunner(host)
    with remote.read_file(yaml_path) as f:
        cfg = f.read()
    table_name = yaml_path.split('/')[-1]
    d = yaml.load_all(cfg, Loader=yaml.FullLoader)
    name_list = []
    shape_list = []
    opt_type_list = []
    offset_list = [0]
    for data in d:
        for var in data['param']:
            name_list.append(var['name'] + ":0")
            shape_list.append((var['rown'], var['coln']))
            offset_list.append(offset_list[-1] + (
                var['coln'] * var['rown']))
            if 'opt_type' in var:
                opt_type_list.append(var['opt_type'])
            else:
                opt_type_list.append(DEFAULT_OPT_TYPE)
    return DenseTable(table_name, name_list, shape_list, offset_list, opt_type_list)

def get_dense_table_from_local_dnn_plugin(yaml_path):
    """
    从 dnn-plugin.yaml的信息中生成 DenseTable

    Args:
        yaml_path (str): 本地的 dnn-plugin.yaml 的地址

    Returns:
         DenseTable: dnn-plugin.yaml 对应的模型的 Dense 参数信息
    """
    with open(yaml_path) as f:
        cfg = f.read()
    table_name = yaml_path.split('/')[-1]
    d = yaml.load_all(cfg, Loader=yaml.FullLoader)
    name_list = []
    shape_list = []
    opt_type_list = []
    offset_list = [0]
    for data in d:
        for var in data['param']:
            name_list.append(var['name'] + ":0")
            shape_list.append((var['rown'], var['coln']))
            offset_list.append(offset_list[-1] + (
                var['coln'] * var['rown']))
            if 'opt_type' in var:
                opt_type_list.append(var['opt_type'])
            else:
                opt_type_list.append(DEFAULT_OPT_TYPE)
    return DenseTable(table_name, name_list, shape_list, offset_list, opt_type_list)


def get_dense_table_from_dict(weight_dict, extra_dict):
    """
    从 weight_dict 和 extra_dict 中生成 DenseTable

    Args:
        weight_dict (dict): weight_dict
        extra_dict (dict): extra_dict

    Returns:
         DenseTable: weight_dict 和 extra_dict 对应的模型的 Dense 参数信息
    """
    name_list = []
    shape_list = []
    opt_type_list = []
    offset_list = [0]
    for name, var in weight_dict.items():
        name_list.append(name)
        shape_list.append(var.shape)
        offset_list.append(offset_list[-1] + (var.shape[0] * var.shape[1]))
        opt_type_list.append(DEFAULT_OPT_TYPE)
    return DenseTable("DenseTable", name_list, shape_list, offset_list, opt_type_list, extra_dict)


def diff_dense_table(dense_table_1, dense_table_2):
    """
    对比两个 DenseTable 的 差别，给出 dense_table_2对于 dense_table_1的区别。
    会打印如下信息：
    1、weight diff
    2、extra diff

    diff 包含以下信息：
    参数名字 + table_1中参数的 shape + table_2中参数的 shape + 变化方式（add、delete、change）

    Args:
        dense_table_1 (DenseTable): 作为 base 的 DenseTable
        dense_table_2 (DenseTable): 新入的，作为对比的 DenseTable

    Returns:
        _type_: _description_
    """
    weight_1, extra_1 = dense_table_1.get_variable_list()
    weight_2, extra_2 = dense_table_2.get_variable_list()

    def get_diff_list(var_dict_1, var_dict_2):
        # 获取 var_dict_1的 key list
        key_list_1 = list(var_dict_1.keys())
        # 获取 var_dict_2的 key list
        key_list_2 = list(var_dict_2.keys())

        # 获取两个 key list 的交集
        key_list_intersection = sorted(list(set(key_list_1) & set(key_list_2)))

        # 获取两个 key list 的差集
        key_list_1_diff = sorted(
            list(set(key_list_1).difference(set(key_list_2))))
        key_list_2_diff = sorted(
            list(set(key_list_2).difference(set(key_list_1))))

        diff = []
        for k in key_list_intersection:
            if var_dict_1[k].shape == var_dict_2[k].shape:
                continue
            if var_dict_1[k].shape != var_dict_2[k].shape:
                diff.append({"name": k, "origin_shape": var_dict_1[k].shape,
                            "current_shape": var_dict_2[k].shape, "type": "change"})

        for k in key_list_1_diff:
            diff.append({"name": k, "origin_shape": var_dict_1[k].shape,
                        "current_shape": None, "type": "delete"})

        for k in key_list_2_diff:
            diff.append({"name": k, "origin_shape": None,
                        "current_shape": var_dict_2[k].shape, "type": "add"})

        return diff

    print("Dense Weight Diff: ")
    weight_diff = get_diff_list(weight_1, weight_2)
    print(pd.DataFrame(weight_diff))

    print("Dense Extra Diff: ")
    extra_diff = get_diff_list(extra_1, extra_2)
    print(pd.DataFrame(extra_diff))
    return weight_diff, extra_diff


def mock_load_dense_func(load_dense_func, warmup_dense_table: DenseTable, current_dense_table: DenseTable, load_option: dict = dict()):
    """
    本函数提供了模拟 load_dense_func 的功能。通过给定的 warmup_dense_table 和 current_dense_table，模拟 load_dense_func 的行为。
    同时提供了 load_option(dict)，可以模拟运行时的 load_option 来控制 load_dense_func 的行为。

    其基本原理是：
    1、通过 warmup_dense_table 和 current_dense_table，获取两个 table 的变量列表
    2、warmup_dense_table 的变量列表作为 load_dense_func 的输入 [warmup_weight, warmup_extra]
    3、current_dense_table 的变量列表作为 load_dense_func 的输入 [ps_weight, ps_extra, tf_weight]
    4、load_option dict 转为getter 的对象传递给load_dense_func
    5、调用 load_dense_func，模拟真实场景的行为，并将结果写在 weight_res.txt 与 extra_res.txt 中

    为简化数值设置，该函数做了以下操作：
    1、warmup_weight 及 warmup_extra 的所有数值被 set 为11，22
    2、ps_weight 及 ps_extra 的所有数值被 set 为33，44
    3、tf_weight 的所有数值被 set 为55

    用户可以观察 load_dense_func 最终产出的结果，来判断 load_dense_func 的行为是否符合预期。

    Args:
        load_dense_func (function): 可调用的，用户自定义的load_dense_func
        warmup_dense_table (DenseTable): 基于 warmup 模型得到的 DenseTable
        current_dense_table (DenseTable): 基于当前模型得到的 DenseTable
        load_option (dict, optional): 可选的 load_option 字典，填写模拟的 key + value，将转为 load_option 传入 load_dense_func

    Returns:
        dict: 通过 load_dense_func 后，最终组合成的weight
        dict: 通过 load_dense_func 后，最终组合成的extra
    """

    # 获取 warmup_dense_table 的变量列表
    warmup_weight, warmup_extra = warmup_dense_table.get_variable_list(
        mock_weight_value=11.0, mock_extra_value=22.0)

    # 获取 current_dense_table 的变量列表
    ps_weight, ps_extra = current_dense_table.get_variable_list(
        mock_weight_value=33.0, mock_extra_value=44.0)
    tf_weight, _ = current_dense_table.get_variable_list(
        mock_weight_value=55.0)

    # 将 load_option 转为 getter 对象
    load_option_obj = Dict2Obj(load_option)

    # 调用 load_dense_func，模拟真实场景的行为，打印结果
    weight, extra = load_dense_func(
        warmup_weight, warmup_extra, ps_weight, ps_extra, tf_weight, load_option_obj)

    with open("weight_res.txt", "w+") as f:
        f.write(str(weight))

    with open("extra_res.txt", "w+") as f:
        f.write(str(extra))

    return weight, extra


