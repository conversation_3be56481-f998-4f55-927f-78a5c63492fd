{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (8013, 1024)   (8077, 1024)  change\n", "1                        share_bottom_layer_new_0/w:0  (14027, 1024)  (14091, 1024)  change\n", "2                                LayerNorm_100/beta:0           None        (32, 1)     add\n", "3                               LayerNorm_100/gamma:0           None        (32, 1)     add\n", "4                                LayerNorm_101/beta:0           None        (32, 1)     add\n", "5                               LayerNorm_101/gamma:0           None        (32, 1)     add\n", "6                                LayerNorm_102/beta:0           None        (32, 1)     add\n", "7                               LayerNorm_102/gamma:0           None        (32, 1)     add\n", "8                                 LayerNorm_89/beta:0           None        (32, 1)     add\n", "9                                LayerNorm_89/gamma:0           None        (32, 1)     add\n", "10                                LayerNorm_90/beta:0           None        (32, 1)     add\n", "11                               LayerNorm_90/gamma:0           None        (32, 1)     add\n", "12                                LayerNorm_91/beta:0           None        (32, 1)     add\n", "13                               LayerNorm_91/gamma:0           None        (32, 1)     add\n", "14                                LayerNorm_92/beta:0           None        (32, 1)     add\n", "15                               LayerNorm_92/gamma:0           None        (32, 1)     add\n", "16                                LayerNorm_93/beta:0           None        (32, 1)     add\n", "17                               LayerNorm_93/gamma:0           None        (32, 1)     add\n", "18                                LayerNorm_94/beta:0           None        (32, 1)     add\n", "19                               LayerNorm_94/gamma:0           None        (32, 1)     add\n", "20                                LayerNorm_95/beta:0           None        (32, 1)     add\n", "21                               LayerNorm_95/gamma:0           None        (32, 1)     add\n", "22                                LayerNorm_96/beta:0           None        (32, 1)     add\n", "23                               LayerNorm_96/gamma:0           None        (32, 1)     add\n", "24                                LayerNorm_97/beta:0           None        (32, 1)     add\n", "25                               LayerNorm_97/gamma:0           None        (32, 1)     add\n", "26                                LayerNorm_98/beta:0           None        (32, 1)     add\n", "27                               LayerNorm_98/gamma:0           None        (32, 1)     add\n", "28                                LayerNorm_99/beta:0           None        (32, 1)     add\n", "29                               LayerNorm_99/gamma:0           None        (32, 1)     add\n", "30      good_show_hardsearch_count_index_list_cl0/b:0           None        (64, 1)     add\n", "31      good_show_hardsearch_count_index_list_cl0/w:0           None      (200, 64)     add\n", "32      good_show_hardsearch_count_index_list_cl1/b:0           None        (32, 1)     add\n", "33      good_show_hardsearch_count_index_list_cl1/w:0           None       (64, 32)     add\n", "34  good_show_hardsearch_embedding_attentionk_tran...           None       (52, 32)     add\n", "35  good_show_hardsearch_embedding_attentionq_tran...           None      (288, 32)     add\n", "36  good_show_hardsearch_embedding_attentionv_tran...           None       (52, 32)     add\n", "37  good_show_hardsearch_embedding_pooling_0/Layer...           None       (256, 1)     add\n", "38  good_show_hardsearch_embedding_pooling_0/Layer...           None       (256, 1)     add\n", "39       good_show_hardsearch_embedding_pooling_0/b:0           None       (256, 1)     add\n", "40       good_show_hardsearch_embedding_pooling_0/w:0           None     (340, 256)     add\n", "41       good_show_hardsearch_embedding_pooling_1/b:0           None        (32, 1)     add\n", "42       good_show_hardsearch_embedding_pooling_1/w:0           None      (256, 32)     add\n", "43   good_show_hardsearch_exposure_ratio_list_cl0/b:0           None        (64, 1)     add\n", "44   good_show_hardsearch_exposure_ratio_list_cl0/w:0           None      (200, 64)     add\n", "45   good_show_hardsearch_exposure_ratio_list_cl1/b:0           None        (32, 1)     add\n", "46   good_show_hardsearch_exposure_ratio_list_cl1/w:0           None       (64, 32)     add\n", "47          good_show_hardsearch_item_id_list_cl0/b:0           None        (64, 1)     add\n", "48          good_show_hardsearch_item_id_list_cl0/w:0           None      (800, 64)     add\n", "49          good_show_hardsearch_item_id_list_cl1/b:0           None        (32, 1)     add\n", "50          good_show_hardsearch_item_id_list_cl1/w:0           None       (64, 32)     add\n", "51            good_show_hardsearch_lagV1_list_cl0/b:0           None        (64, 1)     add\n", "52            good_show_hardsearch_lagV1_list_cl0/w:0           None      (200, 64)     add\n", "53            good_show_hardsearch_lagV1_list_cl1/b:0           None        (32, 1)     add\n", "54            good_show_hardsearch_lagV1_list_cl1/w:0           None       (64, 32)     add\n", "55            good_show_hardsearch_lagV2_list_cl0/b:0           None        (64, 1)     add\n", "56            good_show_hardsearch_lagV2_list_cl0/w:0           None      (200, 64)     add\n", "57            good_show_hardsearch_lagV2_list_cl1/b:0           None        (32, 1)     add\n", "58            good_show_hardsearch_lagV2_list_cl1/w:0           None       (64, 32)     add\n", "59      good_show_hardsearch_pagecode_id_list_cl0/b:0           None        (64, 1)     add\n", "60      good_show_hardsearch_pagecode_id_list_cl0/w:0           None      (200, 64)     add\n", "61      good_show_hardsearch_pagecode_id_list_cl1/b:0           None        (32, 1)     add\n", "62      good_show_hardsearch_pagecode_id_list_cl1/w:0           None       (64, 32)     add\n", "63   good_show_hardsearch_uniform_spu_id_list_cl0/b:0           None        (64, 1)     add\n", "64   good_show_hardsearch_uniform_spu_id_list_cl0/w:0           None      (800, 64)     add\n", "65   good_show_hardsearch_uniform_spu_id_list_cl1/b:0           None        (32, 1)     add\n", "66   good_show_hardsearch_uniform_spu_id_list_cl1/w:0           None       (64, 32)     add\n", "67         good_show_hardsearch_user_embedding/beta:0           None      (5200, 1)     add\n", "68        good_show_hardsearch_user_embedding/gamma:0           None      (5200, 1)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (8205312,)    (8270848,)  change\n", "1                        share_bottom_layer_new_0/w:0  (14363648,)   (14429184,)  change\n", "2                                LayerNorm_100/beta:0         None         (32,)     add\n", "3                               LayerNorm_100/gamma:0         None         (32,)     add\n", "4                                LayerNorm_101/beta:0         None         (32,)     add\n", "5                               LayerNorm_101/gamma:0         None         (32,)     add\n", "6                                LayerNorm_102/beta:0         None         (32,)     add\n", "7                               LayerNorm_102/gamma:0         None         (32,)     add\n", "8                                 LayerNorm_89/beta:0         None         (32,)     add\n", "9                                LayerNorm_89/gamma:0         None         (32,)     add\n", "10                                LayerNorm_90/beta:0         None         (32,)     add\n", "11                               LayerNorm_90/gamma:0         None         (32,)     add\n", "12                                LayerNorm_91/beta:0         None         (32,)     add\n", "13                               LayerNorm_91/gamma:0         None         (32,)     add\n", "14                                LayerNorm_92/beta:0         None         (32,)     add\n", "15                               LayerNorm_92/gamma:0         None         (32,)     add\n", "16                                LayerNorm_93/beta:0         None         (32,)     add\n", "17                               LayerNorm_93/gamma:0         None         (32,)     add\n", "18                                LayerNorm_94/beta:0         None         (32,)     add\n", "19                               LayerNorm_94/gamma:0         None         (32,)     add\n", "20                                LayerNorm_95/beta:0         None         (32,)     add\n", "21                               LayerNorm_95/gamma:0         None         (32,)     add\n", "22                                LayerNorm_96/beta:0         None         (32,)     add\n", "23                               LayerNorm_96/gamma:0         None         (32,)     add\n", "24                                LayerNorm_97/beta:0         None         (32,)     add\n", "25                               LayerNorm_97/gamma:0         None         (32,)     add\n", "26                                LayerNorm_98/beta:0         None         (32,)     add\n", "27                               LayerNorm_98/gamma:0         None         (32,)     add\n", "28                                LayerNorm_99/beta:0         None         (32,)     add\n", "29                               LayerNorm_99/gamma:0         None         (32,)     add\n", "30      good_show_hardsearch_count_index_list_cl0/b:0         None         (64,)     add\n", "31      good_show_hardsearch_count_index_list_cl0/w:0         None      (12800,)     add\n", "32      good_show_hardsearch_count_index_list_cl1/b:0         None         (32,)     add\n", "33      good_show_hardsearch_count_index_list_cl1/w:0         None       (2048,)     add\n", "34  good_show_hardsearch_embedding_attentionk_tran...         None       (1664,)     add\n", "35  good_show_hardsearch_embedding_attentionq_tran...         None       (9216,)     add\n", "36  good_show_hardsearch_embedding_attentionv_tran...         None       (1664,)     add\n", "37  good_show_hardsearch_embedding_pooling_0/Layer...         None        (256,)     add\n", "38  good_show_hardsearch_embedding_pooling_0/Layer...         None        (256,)     add\n", "39       good_show_hardsearch_embedding_pooling_0/b:0         None        (256,)     add\n", "40       good_show_hardsearch_embedding_pooling_0/w:0         None      (87040,)     add\n", "41       good_show_hardsearch_embedding_pooling_1/b:0         None         (32,)     add\n", "42       good_show_hardsearch_embedding_pooling_1/w:0         None       (8192,)     add\n", "43   good_show_hardsearch_exposure_ratio_list_cl0/b:0         None         (64,)     add\n", "44   good_show_hardsearch_exposure_ratio_list_cl0/w:0         None      (12800,)     add\n", "45   good_show_hardsearch_exposure_ratio_list_cl1/b:0         None         (32,)     add\n", "46   good_show_hardsearch_exposure_ratio_list_cl1/w:0         None       (2048,)     add\n", "47          good_show_hardsearch_item_id_list_cl0/b:0         None         (64,)     add\n", "48          good_show_hardsearch_item_id_list_cl0/w:0         None      (51200,)     add\n", "49          good_show_hardsearch_item_id_list_cl1/b:0         None         (32,)     add\n", "50          good_show_hardsearch_item_id_list_cl1/w:0         None       (2048,)     add\n", "51            good_show_hardsearch_lagV1_list_cl0/b:0         None         (64,)     add\n", "52            good_show_hardsearch_lagV1_list_cl0/w:0         None      (12800,)     add\n", "53            good_show_hardsearch_lagV1_list_cl1/b:0         None         (32,)     add\n", "54            good_show_hardsearch_lagV1_list_cl1/w:0         None       (2048,)     add\n", "55            good_show_hardsearch_lagV2_list_cl0/b:0         None         (64,)     add\n", "56            good_show_hardsearch_lagV2_list_cl0/w:0         None      (12800,)     add\n", "57            good_show_hardsearch_lagV2_list_cl1/b:0         None         (32,)     add\n", "58            good_show_hardsearch_lagV2_list_cl1/w:0         None       (2048,)     add\n", "59      good_show_hardsearch_pagecode_id_list_cl0/b:0         None         (64,)     add\n", "60      good_show_hardsearch_pagecode_id_list_cl0/w:0         None      (12800,)     add\n", "61      good_show_hardsearch_pagecode_id_list_cl1/b:0         None         (32,)     add\n", "62      good_show_hardsearch_pagecode_id_list_cl1/w:0         None       (2048,)     add\n", "63   good_show_hardsearch_uniform_spu_id_list_cl0/b:0         None         (64,)     add\n", "64   good_show_hardsearch_uniform_spu_id_list_cl0/w:0         None      (51200,)     add\n", "65   good_show_hardsearch_uniform_spu_id_list_cl1/b:0         None         (32,)     add\n", "66   good_show_hardsearch_uniform_spu_id_list_cl1/w:0         None       (2048,)     add\n", "67         good_show_hardsearch_user_embedding/beta:0         None       (5200,)     add\n", "68        good_show_hardsearch_user_embedding/gamma:0         None       (5200,)     add\n", "{'QUEUE_SOFT/share_bottom_layer_0/w:0': (8013, 1024), 'share_bottom_layer_new_0/w:0': (14027, 1024)}\n", "{'QUEUE_SOFT/share_bottom_layer_0/w:0': (8205312,), 'share_bottom_layer_new_0/w:0': (14363648,)}\n"]}], "source": ["old_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_ue_v1.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_ue_v2.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print(weight_diff_change)\n", "print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    origin_weight = {'QUEUE_SOFT/share_bottom_layer_0/w:0': (8013, 1024), 'share_bottom_layer_new_0/w:0': (14027, 1024)}\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name in origin_weight:\n", "            origin_size = origin_weight[var_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        \n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(good_show_hardsearch_user_embedding/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_user_embedding/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_count_index_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_count_index_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_count_index_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_count_index_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_item_id_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_item_id_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_item_id_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_item_id_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV1_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV1_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV1_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV1_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV2_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV2_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV2_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_lagV2_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_pagecode_id_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_pagecode_id_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_pagecode_id_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_pagecode_id_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_uniform_spu_id_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_uniform_spu_id_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_uniform_spu_id_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_uniform_spu_id_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_89/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_89/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_90/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_90/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_91/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_91/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_92/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_92/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_93/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_93/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_94/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_94/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_95/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_95/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_96/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_96/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_97/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_97/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_98/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_98/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_99/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_99/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_100/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_100/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_101/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_101/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_102/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_102/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) 不存在，其值由QUEUE_SOFT/share_bottom_layer_0/w:0初始化, size is 8270848 and 8270848\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 14429184 and 14429184\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}