{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                           name origin_shape current_shape    type\n", "0                      rocket_upper_layer_2/w:0   (560, 128)    (656, 128)  change\n", "1                        ue_score_sparse/beta:0     (304, 1)      (320, 1)  change\n", "2                       ue_score_sparse/gamma:0     (304, 1)      (320, 1)  change\n", "3    auction_bid_upper_layer_1/LayerNorm/beta:0         None      (256, 1)     add\n", "4   auction_bid_upper_layer_1/LayerNorm/gamma:0         None      (256, 1)     add\n", "5                 auction_bid_upper_layer_1/b:0         None      (256, 1)     add\n", "6                 auction_bid_upper_layer_1/w:0         None    (656, 256)     add\n", "7    auction_bid_upper_layer_2/LayerNorm/beta:0         None      (128, 1)     add\n", "8   auction_bid_upper_layer_2/LayerNorm/gamma:0         None      (128, 1)     add\n", "9                 auction_bid_upper_layer_2/b:0         None      (128, 1)     add\n", "10                auction_bid_upper_layer_2/w:0         None    (256, 128)     add\n", "11                auction_bid_upper_layer_3/b:0         None       (92, 1)     add\n", "12                auction_bid_upper_layer_3/w:0         None     (128, 92)     add\n", "13                   rank_context_sparse/beta:0         None       (80, 1)     add\n", "14                  rank_context_sparse/gamma:0         None       (80, 1)     add\n", "Dense Extra Diff: \n", "                                           name origin_shape current_shape    type\n", "0                      rocket_upper_layer_2/w:0     (71680,)      (83968,)  change\n", "1                        ue_score_sparse/beta:0       (304,)        (320,)  change\n", "2                       ue_score_sparse/gamma:0       (304,)        (320,)  change\n", "3    auction_bid_upper_layer_1/LayerNorm/beta:0         None        (256,)     add\n", "4   auction_bid_upper_layer_1/LayerNorm/gamma:0         None        (256,)     add\n", "5                 auction_bid_upper_layer_1/b:0         None        (256,)     add\n", "6                 auction_bid_upper_layer_1/w:0         None     (167936,)     add\n", "7    auction_bid_upper_layer_2/LayerNorm/beta:0         None        (128,)     add\n", "8   auction_bid_upper_layer_2/LayerNorm/gamma:0         None        (128,)     add\n", "9                 auction_bid_upper_layer_2/b:0         None        (128,)     add\n", "10                auction_bid_upper_layer_2/w:0         None      (32768,)     add\n", "11                auction_bid_upper_layer_3/b:0         None         (92,)     add\n", "12                auction_bid_upper_layer_3/w:0         None      (11776,)     add\n", "13                   rank_context_sparse/beta:0         None         (80,)     add\n", "14                  rank_context_sparse/gamma:0         None         (80,)     add\n"]}, {"data": {"text/plain": ["([{'name': 'rocket_upper_layer_2/w:0',\n", "   'origin_shape': (560, 128),\n", "   'current_shape': (656, 128),\n", "   'type': 'change'},\n", "  {'name': 'ue_score_sparse/beta:0',\n", "   'origin_shape': (304, 1),\n", "   'current_shape': (320, 1),\n", "   'type': 'change'},\n", "  {'name': 'ue_score_sparse/gamma:0',\n", "   'origin_shape': (304, 1),\n", "   'current_shape': (320, 1),\n", "   'type': 'change'},\n", "  {'name': 'auction_bid_upper_layer_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (656, 256),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256, 128),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_3/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (92, 1),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_3/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128, 92),\n", "   'type': 'add'},\n", "  {'name': 'rank_context_sparse/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (80, 1),\n", "   'type': 'add'},\n", "  {'name': 'rank_context_sparse/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (80, 1),\n", "   'type': 'add'}],\n", " [{'name': 'rocket_upper_layer_2/w:0',\n", "   'origin_shape': (71680,),\n", "   'current_shape': (83968,),\n", "   'type': 'change'},\n", "  {'name': 'ue_score_sparse/beta:0',\n", "   'origin_shape': (304,),\n", "   'current_shape': (320,),\n", "   'type': 'change'},\n", "  {'name': 'ue_score_sparse/gamma:0',\n", "   'origin_shape': (304,),\n", "   'current_shape': (320,),\n", "   'type': 'change'},\n", "  {'name': 'auction_bid_upper_layer_1/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (256,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_1/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (167936,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/LayerNorm/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/LayerNorm/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (128,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_2/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (32768,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_3/b:0',\n", "   'origin_shape': None,\n", "   'current_shape': (92,),\n", "   'type': 'add'},\n", "  {'name': 'auction_bid_upper_layer_3/w:0',\n", "   'origin_shape': None,\n", "   'current_shape': (11776,),\n", "   'type': 'add'},\n", "  {'name': 'rank_context_sparse/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (80,),\n", "   'type': 'add'},\n", "  {'name': 'rank_context_sparse/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (80,),\n", "   'type': 'add'}])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["old_yaml = 'suntianyu06_dsp_sty_ue_re_rocket.yaml'\n", "new_yaml = 'xiatian06_dsp_xt_ue_rocket_add_context_v2 (1).yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "diff_dense_table(dense_old, dense_new)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'rocket_upper_layer_2/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:560, :] = ori_weight\n", "            new_extra[:560, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'ue_score_sparse/beta:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:304, :] = ori_weight\n", "            new_extra[:304, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'ue_score_sparse/gamma:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:304, :] = ori_weight\n", "            new_extra[:304, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(ue_score_sparse/beta:0) 不存在，其值由ue_score_sparse/beta:0初始化, size is 320 and 320\n", "加载的 dense variable(ue_score_sparse/gamma:0) 不存在，其值由ue_score_sparse/gamma:0初始化, size is 320 and 320\n", "加载的 dense variable(rank_context_sparse/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(rank_context_sparse/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(rocket_upper_layer_2/w:0) 不存在，其值由rocket_upper_layer_2/w:0初始化, size is 83968 and 83968\n", "加载的 dense variable(auction_bid_upper_layer_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_1/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_1/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_2/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_2/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_2/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_2/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_3/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(auction_bid_upper_layer_3/b:0) 不存在，其值全新初始化\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}