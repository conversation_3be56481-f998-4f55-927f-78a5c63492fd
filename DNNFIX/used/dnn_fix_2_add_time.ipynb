{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                   name   origin_shape  current_shape    type\n", "0   QUEUE_SOFT/share_bottom_layer_0/w:0   (7885, 1024)   (7949, 1024)  change\n", "1          share_bottom_layer_new_0/w:0  (13899, 1024)  (13963, 1024)  change\n", "2  cal_time_embedding/hour_emb_matrix:0           None       (24, 64)     add\n", "3               request_time_emb/beta:0           None        (64, 1)     add\n", "4              request_time_emb/gamma:0           None        (64, 1)     add\n", "Dense Extra Diff: \n", "                                   name origin_shape current_shape    type\n", "0   QUEUE_SOFT/share_bottom_layer_0/w:0   (8074240,)    (8139776,)  change\n", "1          share_bottom_layer_new_0/w:0  (14232576,)   (14298112,)  change\n", "2  cal_time_embedding/hour_emb_matrix:0         None       (1536,)     add\n", "3               request_time_emb/beta:0         None         (64,)     add\n", "4              request_time_emb/gamma:0         None         (64,)     add\n"]}, {"data": {"text/plain": ["([{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (7885, 1024),\n", "   'current_shape': (7949, 1024),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (13899, 1024),\n", "   'current_shape': (13963, 1024),\n", "   'type': 'change'},\n", "  {'name': 'cal_time_embedding/hour_emb_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (24, 64),\n", "   'type': 'add'},\n", "  {'name': 'request_time_emb/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'},\n", "  {'name': 'request_time_emb/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64, 1),\n", "   'type': 'add'}],\n", " [{'name': 'QUEUE_SOFT/share_bottom_layer_0/w:0',\n", "   'origin_shape': (8074240,),\n", "   'current_shape': (8139776,),\n", "   'type': 'change'},\n", "  {'name': 'share_bottom_layer_new_0/w:0',\n", "   'origin_shape': (14232576,),\n", "   'current_shape': (14298112,),\n", "   'type': 'change'},\n", "  {'name': 'cal_time_embedding/hour_emb_matrix:0',\n", "   'origin_shape': None,\n", "   'current_shape': (1536,),\n", "   'type': 'add'},\n", "  {'name': 'request_time_emb/beta:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'},\n", "  {'name': 'request_time_emb/gamma:0',\n", "   'origin_shape': None,\n", "   'current_shape': (64,),\n", "   'type': 'add'}])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["old_yaml = 'yipeng_cart_re_rb_mix'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_cart_add_emb'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml+'.yaml')\n", "new_yaml = os.path.join(dirpath, new_yaml+'.yaml')\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "diff_dense_table(dense_old, dense_new)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'QUEUE_SOFT/share_bottom_layer_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:7885, :] = ori_weight\n", "            new_extra[:7885, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name == 'share_bottom_layer_new_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:13899, :] = ori_weight\n", "            new_extra[:13899, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(cal_time_embedding/hour_emb_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(request_time_emb/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(request_time_emb/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) 不存在，其值由QUEUE_SOFT/share_bottom_layer_0/w:0初始化, size is 8139776 and 8139776\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 14298112 and 14298112\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}