{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0      good_click_cate2cate_attentionq_trans_matrix:0      (288, 32)      (512, 32)  change\n", "1   good_click_order_embedding_attentionq_trans_ma...      (288, 32)      (512, 32)  change\n", "2   good_click_order_embedding_sumpooling_pooling_...     (348, 256)     (572, 256)  change\n", "3     good_click_softsearch_attentionq_trans_matrix:0      (288, 32)      (512, 32)  change\n", "4   good_show_user_embedding_attentionq_trans_matr...      (288, 32)      (512, 32)  change\n", "5              good_show_user_embedding_pooling_0/w:0     (336, 256)     (560, 256)  change\n", "6                        share_bottom_layer_new_0/w:0  (14027, 1024)  (14251, 1024)  change\n", "7                                       b_item_feas:0           None       (208, 1)     add\n", "8                               product_id_emb/beta:0           None        (16, 1)     add\n", "9                              product_id_emb/gamma:0           None        (16, 1)     add\n", "10                                      w_item_feas:0           None     (208, 208)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0      good_click_cate2cate_attentionq_trans_matrix:0      (9216,)      (16384,)  change\n", "1   good_click_order_embedding_attentionq_trans_ma...      (9216,)      (16384,)  change\n", "2   good_click_order_embedding_sumpooling_pooling_...     (89088,)     (146432,)  change\n", "3     good_click_softsearch_attentionq_trans_matrix:0      (9216,)      (16384,)  change\n", "4   good_show_user_embedding_attentionq_trans_matr...      (9216,)      (16384,)  change\n", "5              good_show_user_embedding_pooling_0/w:0     (86016,)     (143360,)  change\n", "6                        share_bottom_layer_new_0/w:0  (14363648,)   (14593024,)  change\n", "7                                       b_item_feas:0         None        (208,)     add\n", "8                               product_id_emb/beta:0         None         (16,)     add\n", "9                              product_id_emb/gamma:0         None         (16,)     add\n", "10                                      w_item_feas:0         None      (43264,)     add\n", "{'good_click_cate2cate_attentionq_trans_matrix:0': (288, 32), 'good_click_order_embedding_attentionq_trans_matrix:0': (288, 32), 'good_click_order_embedding_sumpooling_pooling_0/w:0': (348, 256), 'good_click_softsearch_attentionq_trans_matrix:0': (288, 32), 'good_show_user_embedding_attentionq_trans_matrix:0': (288, 32), 'good_show_user_embedding_pooling_0/w:0': (336, 256), 'share_bottom_layer_new_0/w:0': (14027, 1024)}\n", "{'good_click_cate2cate_attentionq_trans_matrix:0': (9216,), 'good_click_order_embedding_attentionq_trans_matrix:0': (9216,), 'good_click_order_embedding_sumpooling_pooling_0/w:0': (89088,), 'good_click_softsearch_attentionq_trans_matrix:0': (9216,), 'good_show_user_embedding_attentionq_trans_matrix:0': (9216,), 'good_show_user_embedding_pooling_0/w:0': (86016,), 'share_bottom_layer_new_0/w:0': (14363648,)}\n"]}], "source": ["old_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_ue_v1.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_fix_goods_input_v1.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print(weight_diff_change)\n", "print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func_example(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    origin_weight = {'good_click_cate2cate_attentionq_trans_matrix:0': (288, 32), \n", "                     'good_click_order_embedding_attentionq_trans_matrix:0': (288, 32), \n", "                     'good_click_order_embedding_sumpooling_pooling_0/w:0': (348, 256), \n", "                     'good_click_softsearch_attentionq_trans_matrix:0': (288, 32), \n", "                     'good_show_user_embedding_attentionq_trans_matrix:0': (288, 32), \n", "                     'good_show_user_embedding_pooling_0/w:0': (336, 256), \n", "                     'share_bottom_layer_new_0/w:0': (14027, 1024)}\n", "\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name in origin_weight:\n", "            origin_size = origin_weight[var_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        \n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "    \n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    origin_weight = {'good_click_cate2cate_attentionq_trans_matrix:0': (288, 32), \n", "                    'good_click_order_embedding_attentionq_trans_matrix:0': (288, 32), \n", "                    'good_click_order_embedding_sumpooling_pooling_0/w:0': (348, 256), \n", "                    'good_click_softsearch_attentionq_trans_matrix:0': (288, 32), \n", "                    'good_show_user_embedding_attentionq_trans_matrix:0': (288, 32), \n", "                    'good_show_user_embedding_pooling_0/w:0': (336, 256), \n", "                    'share_bottom_layer_new_0/w:0': (14027, 1024)}\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name == 'share_bottom_layer_new_0/w:0':\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行前半段赋值\n", "            new_weight[:8421, :] = ori_weight[:8421, :]\n", "            new_extra[:8421, :,:] = ori_extra[:8421, :,:]\n", "\n", "            # 进行后半段赋值\n", "            new_weight[8645:, :] = ori_weight[8421:, :]\n", "            new_extra[8645:, :,:] = ori_extra[8421:, :,:]\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in ['good_click_order_embedding_sumpooling_pooling_0/w:0', 'good_show_user_embedding_pooling_0/w:0']:\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行前半段赋值\n", "            new_weight[:288, :] = ori_weight[:288, :]\n", "            new_extra[:288, :,:] = ori_extra[:288, :,:]\n", "\n", "            # 进行后半段赋值\n", "            new_weight[512:, :] = ori_weight[288:, :]\n", "            new_extra[512:, :,:] = ori_extra[288:, :,:]\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        elif var_name in origin_weight:\n", "            origin_size = origin_weight[var_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        \n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(product_id_emb/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(product_id_emb/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(w_item_feas:0) 不存在，其值全新初始化\n", "加载的 dense variable(b_item_feas:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_user_embedding_pooling_0/w:0) 不存在，其值由good_show_user_embedding_pooling_0/w:0初始化, size is 143360 and 143360\n", "加载的 dense variable(good_show_user_embedding_attentionq_trans_matrix:0) 不存在，其值由good_show_user_embedding_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(good_click_order_embedding_sumpooling_pooling_0/w:0) 不存在，其值由good_click_order_embedding_sumpooling_pooling_0/w:0初始化, size is 146432 and 146432\n", "加载的 dense variable(good_click_order_embedding_attentionq_trans_matrix:0) 不存在，其值由good_click_order_embedding_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(good_click_cate2cate_attentionq_trans_matrix:0) 不存在，其值由good_click_cate2cate_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(good_click_softsearch_attentionq_trans_matrix:0) 不存在，其值由good_click_softsearch_attentionq_trans_matrix:0初始化, size is 16384 and 16384\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 14593024 and 14593024\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}