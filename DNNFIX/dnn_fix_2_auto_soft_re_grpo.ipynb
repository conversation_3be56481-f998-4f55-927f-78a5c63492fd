{"cells": [{"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "dirpath = \"/Users/<USER>/Downloads\"\n", "sys.path.append(dirpath)\n", "from pylib.dnn_fix_v2 import *"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dense Weight Diff: \n", "                                                 name   origin_shape  current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (9037, 1024)   (9165, 1024)  change\n", "1                        share_bottom_layer_new_0/w:0  (15051, 1024)  (15179, 1024)  change\n", "2                                 LayerNorm_71/beta:0           None        (32, 1)     add\n", "3                                LayerNorm_71/gamma:0           None        (32, 1)     add\n", "4                                 LayerNorm_72/beta:0           None        (32, 1)     add\n", "5                                LayerNorm_72/gamma:0           None        (32, 1)     add\n", "6                                 LayerNorm_73/beta:0           None        (32, 1)     add\n", "7                                LayerNorm_73/gamma:0           None        (32, 1)     add\n", "8                                 LayerNorm_74/beta:0           None        (32, 1)     add\n", "9                                LayerNorm_74/gamma:0           None        (32, 1)     add\n", "10                                LayerNorm_75/beta:0           None        (32, 1)     add\n", "11                               LayerNorm_75/gamma:0           None        (32, 1)     add\n", "12                                LayerNorm_76/beta:0           None        (32, 1)     add\n", "13                               LayerNorm_76/gamma:0           None        (32, 1)     add\n", "14                                LayerNorm_77/beta:0           None        (32, 1)     add\n", "15                               LayerNorm_77/gamma:0           None        (32, 1)     add\n", "16                                LayerNorm_78/beta:0           None        (32, 1)     add\n", "17                               LayerNorm_78/gamma:0           None        (32, 1)     add\n", "18    good_click_softsearch_attentionk_trans_matrix:0           None       (72, 32)     add\n", "19    good_click_softsearch_attentionq_trans_matrix:0           None      (288, 32)     add\n", "20    good_click_softsearch_attentionv_trans_matrix:0           None       (72, 32)     add\n", "21  good_click_softsearch_item_count_list_top50_cl...           None        (64, 1)     add\n", "22  good_click_softsearch_item_count_list_top50_cl...           None      (100, 64)     add\n", "23  good_click_softsearch_item_count_list_top50_cl...           None        (32, 1)     add\n", "24  good_click_softsearch_item_count_list_top50_cl...           None       (64, 32)     add\n", "25  good_click_softsearch_layer_new_0/LayerNorm/be...           None       (128, 1)     add\n", "26  good_click_softsearch_layer_new_0/LayerNorm/ga...           None       (128, 1)     add\n", "27              good_click_softsearch_layer_new_0/b:0           None       (128, 1)     add\n", "28              good_click_softsearch_layer_new_0/w:0           None      (72, 128)     add\n", "29  good_click_softsearch_layer_new_1/LayerNorm/be...           None        (64, 1)     add\n", "30  good_click_softsearch_layer_new_1/LayerNorm/ga...           None        (64, 1)     add\n", "31              good_click_softsearch_layer_new_1/b:0           None        (64, 1)     add\n", "32              good_click_softsearch_layer_new_1/w:0           None      (128, 64)     add\n", "33              good_click_softsearch_layer_new_2/b:0           None        (32, 1)     add\n", "34              good_click_softsearch_layer_new_2/w:0           None       (64, 32)     add\n", "35         good_click_softsearch_topk_indices_cl0/b:0           None        (64, 1)     add\n", "36         good_click_softsearch_topk_indices_cl0/w:0           None      (100, 64)     add\n", "37         good_click_softsearch_topk_indices_cl1/b:0           None        (32, 1)     add\n", "38         good_click_softsearch_topk_indices_cl1/w:0           None       (64, 32)     add\n", "39          good_click_softsearch_topk_values_cl0/b:0           None        (64, 1)     add\n", "40          good_click_softsearch_topk_values_cl0/w:0           None      (100, 64)     add\n", "41          good_click_softsearch_topk_values_cl1/b:0           None        (32, 1)     add\n", "42          good_click_softsearch_topk_values_cl1/w:0           None       (64, 32)     add\n", "43  good_show_hardsearch_embedding_attentionk_tran...           None       (52, 32)     add\n", "44  good_show_hardsearch_embedding_attentionq_tran...           None      (288, 32)     add\n", "45  good_show_hardsearch_embedding_attentionv_tran...           None       (52, 32)     add\n", "46  good_show_hardsearch_embedding_pooling_0/Layer...           None       (256, 1)     add\n", "47  good_show_hardsearch_embedding_pooling_0/Layer...           None       (256, 1)     add\n", "48       good_show_hardsearch_embedding_pooling_0/b:0           None       (256, 1)     add\n", "49       good_show_hardsearch_embedding_pooling_0/w:0           None     (340, 256)     add\n", "50       good_show_hardsearch_embedding_pooling_1/b:0           None        (32, 1)     add\n", "51       good_show_hardsearch_embedding_pooling_1/w:0           None      (256, 32)     add\n", "52   good_show_hardsearch_exposure_ratio_list_cl0/b:0           None        (64, 1)     add\n", "53   good_show_hardsearch_exposure_ratio_list_cl0/w:0           None      (200, 64)     add\n", "54   good_show_hardsearch_exposure_ratio_list_cl1/b:0           None        (32, 1)     add\n", "55   good_show_hardsearch_exposure_ratio_list_cl1/w:0           None       (64, 32)     add\n", "Dense Extra Diff: \n", "                                                 name origin_shape current_shape    type\n", "0                 QUEUE_SOFT/share_bottom_layer_0/w:0   (9253888,)    (9384960,)  change\n", "1                        share_bottom_layer_new_0/w:0  (15412224,)   (15543296,)  change\n", "2                                 LayerNorm_71/beta:0         None         (32,)     add\n", "3                                LayerNorm_71/gamma:0         None         (32,)     add\n", "4                                 LayerNorm_72/beta:0         None         (32,)     add\n", "5                                LayerNorm_72/gamma:0         None         (32,)     add\n", "6                                 LayerNorm_73/beta:0         None         (32,)     add\n", "7                                LayerNorm_73/gamma:0         None         (32,)     add\n", "8                                 LayerNorm_74/beta:0         None         (32,)     add\n", "9                                LayerNorm_74/gamma:0         None         (32,)     add\n", "10                                LayerNorm_75/beta:0         None         (32,)     add\n", "11                               LayerNorm_75/gamma:0         None         (32,)     add\n", "12                                LayerNorm_76/beta:0         None         (32,)     add\n", "13                               LayerNorm_76/gamma:0         None         (32,)     add\n", "14                                LayerNorm_77/beta:0         None         (32,)     add\n", "15                               LayerNorm_77/gamma:0         None         (32,)     add\n", "16                                LayerNorm_78/beta:0         None         (32,)     add\n", "17                               LayerNorm_78/gamma:0         None         (32,)     add\n", "18    good_click_softsearch_attentionk_trans_matrix:0         None       (2304,)     add\n", "19    good_click_softsearch_attentionq_trans_matrix:0         None       (9216,)     add\n", "20    good_click_softsearch_attentionv_trans_matrix:0         None       (2304,)     add\n", "21  good_click_softsearch_item_count_list_top50_cl...         None         (64,)     add\n", "22  good_click_softsearch_item_count_list_top50_cl...         None       (6400,)     add\n", "23  good_click_softsearch_item_count_list_top50_cl...         None         (32,)     add\n", "24  good_click_softsearch_item_count_list_top50_cl...         None       (2048,)     add\n", "25  good_click_softsearch_layer_new_0/LayerNorm/be...         None        (128,)     add\n", "26  good_click_softsearch_layer_new_0/LayerNorm/ga...         None        (128,)     add\n", "27              good_click_softsearch_layer_new_0/b:0         None        (128,)     add\n", "28              good_click_softsearch_layer_new_0/w:0         None       (9216,)     add\n", "29  good_click_softsearch_layer_new_1/LayerNorm/be...         None         (64,)     add\n", "30  good_click_softsearch_layer_new_1/LayerNorm/ga...         None         (64,)     add\n", "31              good_click_softsearch_layer_new_1/b:0         None         (64,)     add\n", "32              good_click_softsearch_layer_new_1/w:0         None       (8192,)     add\n", "33              good_click_softsearch_layer_new_2/b:0         None         (32,)     add\n", "34              good_click_softsearch_layer_new_2/w:0         None       (2048,)     add\n", "35         good_click_softsearch_topk_indices_cl0/b:0         None         (64,)     add\n", "36         good_click_softsearch_topk_indices_cl0/w:0         None       (6400,)     add\n", "37         good_click_softsearch_topk_indices_cl1/b:0         None         (32,)     add\n", "38         good_click_softsearch_topk_indices_cl1/w:0         None       (2048,)     add\n", "39          good_click_softsearch_topk_values_cl0/b:0         None         (64,)     add\n", "40          good_click_softsearch_topk_values_cl0/w:0         None       (6400,)     add\n", "41          good_click_softsearch_topk_values_cl1/b:0         None         (32,)     add\n", "42          good_click_softsearch_topk_values_cl1/w:0         None       (2048,)     add\n", "43  good_show_hardsearch_embedding_attentionk_tran...         None       (1664,)     add\n", "44  good_show_hardsearch_embedding_attentionq_tran...         None       (9216,)     add\n", "45  good_show_hardsearch_embedding_attentionv_tran...         None       (1664,)     add\n", "46  good_show_hardsearch_embedding_pooling_0/Layer...         None        (256,)     add\n", "47  good_show_hardsearch_embedding_pooling_0/Layer...         None        (256,)     add\n", "48       good_show_hardsearch_embedding_pooling_0/b:0         None        (256,)     add\n", "49       good_show_hardsearch_embedding_pooling_0/w:0         None      (87040,)     add\n", "50       good_show_hardsearch_embedding_pooling_1/b:0         None         (32,)     add\n", "51       good_show_hardsearch_embedding_pooling_1/w:0         None       (8192,)     add\n", "52   good_show_hardsearch_exposure_ratio_list_cl0/b:0         None         (64,)     add\n", "53   good_show_hardsearch_exposure_ratio_list_cl0/w:0         None      (12800,)     add\n", "54   good_show_hardsearch_exposure_ratio_list_cl1/b:0         None         (32,)     add\n", "55   good_show_hardsearch_exposure_ratio_list_cl1/w:0         None       (2048,)     add\n", "\n", "粘贴到my_load_dense_func开头的origin_weight处\n", "{'QUEUE_SOFT/share_bottom_layer_0/w:0': (9037, 1024), 'share_bottom_layer_new_0/w:0': (15051, 1024)}\n"]}], "source": ["old_yaml = 'yipeng_cart_order_re_grpo_v.yaml'\n", "new_yaml = 'xiatian06_dsp_lps_sv_xt_good_click_soft_re_grpo_v1.yaml'\n", "\n", "old_yaml = os.path.join(dirpath, old_yaml)\n", "new_yaml = os.path.join(dirpath, new_yaml)\n", "dense_old = get_dense_table_from_local_dnn_plugin(yaml_path=old_yaml)\n", "# print(dense_old)\n", "dense_new = get_dense_table_from_local_dnn_plugin(yaml_path=new_yaml)\n", "# print(dense_new)\n", "weight_diff, extra_diff = diff_dense_table(dense_old, dense_new)\n", "\n", "weight_diff_change = {w['name']:w['origin_shape'] for w in weight_diff if w['type'] == 'change'}\n", "extra_diff_change = {w['name']:w['origin_shape'] for w in extra_diff if w['type'] == 'change'}\n", "print()\n", "print('粘贴到my_load_dense_func开头的origin_weight处')\n", "print(weight_diff_change)\n", "# print(extra_diff_change)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def my_load_dense_func(warmup_weight: dict, warmup_extra: dict, ps_weight: dict, ps_extra: dict, tf_weight: dict, load_option):\n", "    # type==change，原来的小尺寸、现在变大的weight和它的原始shape\n", "    origin_weight = {'QUEUE_SOFT/share_bottom_layer_0/w:0': (9037, 1024), 'share_bottom_layer_new_0/w:0': (15051, 1024)}\n", "\n", "    # weight是权重，extra是优化器相关参数，也与权重尺寸有关。 ps_weight和tf_weight里两种不同初始化算法，主要看tf_weight。\n", "    def extra_reshape(weight, extra):\n", "        exShape = list(weight.shape)\n", "        exShape.append(-1)\n", "        extra = extra[:weight.size].reshape(exShape)\n", "        return extra\n", "    \n", "    def get_extra(extra):\n", "        return extra.reshape([-1])\n", "    \n", "    print('my_load_dense_func')\n", "    weight = None\n", "    extra = None\n", "    dense_variable_nums = len(tf_weight)\n", "    \n", "    # 第一层的权重的尺寸可能不一样，取决于输入\n", "    # 假设旧权重尺寸小，那就要拿旧权重+剩下部分新权重作为热启动权重。\n", "    # 假设旧权重尺寸大，那就只取新权重大小部分的旧权重，注意怎么取的逻辑。\n", "\n", "    # 这里是旧权重尺寸小(或无）的情况\n", "    for var_name in list(tf_weight): \n", "        if var_name in origin_weight:\n", "            origin_size = origin_weight[var_name][0]\n", "            # 原来的 weight\n", "            ori_weight = warmup_weight[var_name]\n", "            ori_extra = extra_reshape(ori_weight, warmup_extra[var_name])\n", "            # 新的 weight\n", "            new_weight = tf_weight[var_name]\n", "            new_extra = extra_reshape(new_weight, ps_extra[var_name])\n", "            # 进行赋值\n", "            new_weight[:origin_size, :] = ori_weight\n", "            new_extra[:origin_size, :,:] = ori_extra\n", "            # 回填\n", "            warmup_weight[var_name]  = new_weight\n", "            warmup_extra[var_name] = get_extra(new_extra)\n", "            print(\"加载的 dense variable({}) 不存在，其值由{}初始化, size is {} and {}\".format(\n", "                    var_name, var_name, tf_weight[var_name].size, ps_extra[var_name].size))\n", "        \n", "        elif var_name not in warmup_weight:\n", "            print(\"加载的 dense variable({}) 不存在，其值全新初始化\".format(var_name))\n", "            warmup_weight[var_name] = tf_weight[var_name]\n", "            warmup_extra[var_name] = ps_extra[var_name]\n", "\n", "            \n", "    if len(warmup_weight) > 0:\n", "        for var_name in list(warmup_weight):\n", "            if var_name not in tf_weight:\n", "                print(\"加载的 dense variable({}) 在运行时不存在，其值被忽略。\".format(var_name))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "#                     if var_name not in warmup_weight:\n", "#                         print(f\"{var_name} not in warmup_weight\")\n", "#                     else:\n", "#                         print(f\"{var_name} not in warmup_extra\")\n", "                    pass\n", "            elif warmup_weight[var_name].size != tf_weight[var_name].size:\n", "                # 这里可以添加旧权重尺寸大的处理逻辑\n", "                print(\"加载的 dense variable({}) size ({} vs {}) 不匹配，其值被忽略\".format(var_name, warmup_weight[var_name].size, tf_weight[var_name].size))  # noqa\n", "                try:\n", "                    del warmup_weight[var_name]\n", "                    del warmup_extra[var_name]\n", "                except KeyError as e:\n", "                    if var_name not in warmup_weight:\n", "                        print(f\"{var_name} not in warmup_weight\")\n", "                    else:\n", "                        print(f\"{var_name} not in warmup_extra\")\n", "        weight = warmup_weight\n", "        extra = warmup_extra\n", "    else:\n", "        weight = tf_weight\n", "        extra = ps_extra\n", "\n", "    assert len(weight) == dense_variable_nums\n", "    assert len(extra) == dense_variable_nums\n", "\n", "    print('end my_load_dense_func')\n", "    return weight, extra"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["my_load_dense_func\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_exposure_ratio_list_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_pooling_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_show_hardsearch_embedding_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_71/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_72/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_list_top50_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_list_top50_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_list_top50_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_item_count_list_top50_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_73/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_74/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_values_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_75/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_75/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_76/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_76/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_cl0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_cl0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_cl1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_topk_indices_cl1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_77/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_77/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_78/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(LayerNorm_78/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_0/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/beta:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_1/LayerNorm/gamma:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/w:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_layer_new_2/b:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionq_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionk_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(good_click_softsearch_attentionv_trans_matrix:0) 不存在，其值全新初始化\n", "加载的 dense variable(QUEUE_SOFT/share_bottom_layer_0/w:0) 不存在，其值由QUEUE_SOFT/share_bottom_layer_0/w:0初始化, size is 9384960 and 9384960\n", "加载的 dense variable(share_bottom_layer_new_0/w:0) 不存在，其值由share_bottom_layer_new_0/w:0初始化, size is 15543296 and 15543296\n", "end my_load_dense_func\n", "Dense Weight Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n", "Dense Extra Diff: \n", "Empty DataFrame\n", "Columns: []\n", "Index: []\n"]}, {"data": {"text/plain": ["([], [])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["res_weight, res_extra = mock_load_dense_func(my_load_dense_func, dense_old, dense_new)\n", "dense_change = get_dense_table_from_dict(res_weight, res_extra)\n", "diff_dense_table(dense_new, dense_change)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}