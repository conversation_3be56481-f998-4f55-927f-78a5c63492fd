#!/bin/bash

# 使用数组去重
declare -A unique_urls
urls=(
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/mmu"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/teams/reco_follow"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/teams/aiplatform"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/BladeDisc"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/faiss-1.7.3"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/scann_1.2.9"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-gpu"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-2.4.1"
    "https://devsvn.corp.kuaishou.com:2014/codebase/ks/third_party/tensorflow-2.10.0"
)

# 去重
for url in "${urls[@]}"; do
    unique_urls["$url"]=1
done

p=$(pwd)
checkout_dir="my_checkout"

for url in "${!unique_urls[@]}"; do
    cd "$p" || exit 1
    
    echo "处理 $url"
    
    # 清理已存在的目录
    if [ -d "$checkout_dir" ]; then
        rm -rf "$checkout_dir"
    fi
    
    # 执行svn checkout
    if ! svn checkout --depth=empty "$url" "$checkout_dir"; then
        echo "错误: 无法检出 $url"
        continue
    fi
    
    # 更新OWNERS文件并提交
    cd "$checkout_dir" || continue
    
    if ! svn update OWNERS 2>/dev/null; then
        echo "警告: $url 没有 OWNERS 文件"
        cd "$p"
        rm -rf "$checkout_dir"
        continue
    fi
    
    if ! grep -q "@ks_owners" OWNERS; then
        echo "@ks_owners" >> OWNERS
        if ! svn commit OWNERS -m "add ks_owners group"; then
            echo "错误: 无法提交 OWNERS 文件到 $url"
        fi
    fi
    
    # 清理
    cd "$p" || exit 1
    rm -rf "$checkout_dir"
done
