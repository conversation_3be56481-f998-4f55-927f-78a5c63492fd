text = """
column_name = good_show_category1, compression_type = None, column_type = List<Long>
column_name = good_show_category2, compression_type = None, column_type = List<Long>
column_name = good_show_category3, compression_type = None, column_type = List<Long>
column_name = good_show_category4, compression_type = None, column_type = List<Long>
column_name = good_show_commodity_id, compression_type = None, column_type = List<Long>
column_name = good_show_detail_content_stay_time, compression_type = None, column_type = List<Long>
column_name = good_show_exposure_ratio, compression_type = None, column_type = List<Long>
column_name = good_show_exposure_time, compression_type = None, column_type = List<Long>
column_name = good_show_hour_of_day, compression_type = None, column_type = List<Long>
column_name = good_show_leaf_category, compression_type = None, column_type = List<Long>
column_name = good_show_pagecode_id, compression_type = None, column_type = List<Long>
column_name = good_show_seller_id, compression_type = None, column_type = List<Long>
column_name = good_show_time_gap_hour, compression_type = None, column_type = List<Long>
column_name = good_show_uniform_spu_id, compression_type = None, column_type = List<Long>
""".strip().splitlines()

for l in text:
    column, compression, column_type = l.split(',')
    name = column.split('=')[-1].strip()
    column_type = 'array<bigint>,'
    print(name, column_type)