import pandas as pd

def parse_importance(text):
    parse_set = {}
    text = text.strip()
    for line in text.split("\n"):
        if 'first_stage_ctcvr_' not in line:
            continue
        line = line.strip()
        if ":" in line:
            prefix, importance = line.split(":")
            prefix = prefix.strip()
            importance = importance.strip()
        else:
            continue
        try:
            importance = float(importance)
        except Exception as e:
            continue
        infos = prefix.split(" ")
        if len(infos) >= 2:
            names = infos[1]
        else:
            continue
        infos = infos[2:]
        info_set = {}
        for i in range(int(len(infos) / 2)):
            info_set[infos[i * 2]] = infos[i * 2 + 1]

        if "_pos" in info_set:
            position = int(info_set["_pos"])
        else:
            position = -1
        if "_slot" in info_set:
            slot = int(info_set["_slot"])
        else:
            slot = -1
        y_name, x_name = names.split("__")
        if y_name not in parse_set:
            parse_set[y_name] = {}
        if x_name not in parse_set[y_name]:
            parse_set[y_name][x_name] = {}
        if slot not in parse_set[y_name][x_name]:
            parse_set[y_name][x_name][slot] = {}
        if position not in parse_set[y_name][x_name][slot]:
            parse_set[y_name][x_name][slot][position] = importance
            # print(parse_set[y_name][x_name][slot][position])
        else:
            if importance != parse_set[y_name][x_name][slot][-1]:
                print('冲突', importance, parse_set[y_name][x_name][slot])
            continue
            # raise Exception("{} pos {} slot {} 冲突，old: {}, new {}".format(names, position, slot, parse_set[y_name][x_name][position], importance))
    df = {
        "y_name": [],
        "x_name": [],
        "slot": [],
        "position": [],
        "importance": []
    }
    # print("{}\t{}\t{}\t{}\t{}".format("y_name", "x_name", "slot", "position", "importance"))
    for y_name in parse_set:
        for x_name in parse_set[y_name]:
            for slot in parse_set[y_name][x_name]:
                for position in parse_set[y_name][x_name][slot]:
                    importance = parse_set[y_name][x_name][slot][position]
                    df["y_name"].append(y_name)
                    df["x_name"].append(x_name)
                    df["slot"].append(slot)
                    df["position"].append(position)
                    df["importance"].append(importance)
                    # print("{}\t{}\t{}\t{}\t{}".format(y_name, x_name, slot, position, importance))
    df = pd.DataFrame(df)
    df.to_csv("importance.csv", index=False)


fields = """x, grad, x_mean, delta_y, weight, delta_y_weight, delta_y_avg""".replace(',',' ').split()

def parse_log_efficient(log_path, output_csv):
    blocks = []
    current_block = {}
    recording = False
    name = ""

    with open(log_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()

            if line.__contains__('stdout ecom_ctr__'):
                recording = True
                current_block = {}
                name = line.split("stdout ecom_ctr__", 1)[-1]
                current_block["name"] = name
                para_lines = 0
                continue

            if recording:
                if line.__contains__("stdout ####################"):
                    blocks.append(current_block)
                    recording = False
                else:
                    para_lines += 1
                    line = line.split('stdout ')[-1]
                    if line.__contains__(':'):
                        field, text = line.split(':', 1)
                        if field in fields:
                            text = text.split('Tensor("')[-1]
                            # text = text.rsplit(')')[0]
                            current_block[field] = text
                    else:
                        print(line)
                # 最后一段可能未封闭
                if para_lines >= 100:
                    break

    df = pd.DataFrame(blocks)
    df.to_csv(output_csv, index=False)
    print(df)
    print(f"成功提取 {len(blocks)} 段并保存至 {output_csv}")

# 用法示例：
# parse_log_efficient('your_large_log.txt', 'output.csv')


if __name__ == '__main__':
    # parse_log_efficient('/Users/<USER>/Downloads/kml-task-573825-record-10872824-prod-launcher-0.log', 'output.csv')
    # stdout 2025-05-08 19:35:42: basic_hook.py:623 Test mode, step: 2000, samples: 1024, using time: 2.33, total samples: 2048000, avg speed:  411.99 samples/s

    with open('log','r') as f:
        text = f.read()
    parse_importance(text)