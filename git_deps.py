dragon_deps = """
[git_deps]
"//algo-engine/kaiworks/kaiworks-stream-proto" = "*************************:algo-engine/kaiworks/kaiworks-stream-proto.git"
"//dragon" = "*************************:reco-cpp/dragon.git"
"//ks/realtime_reco" = "*************************:reco-cpp/realtime_reco.git"
"//ks/reco/merchant_ep_center" = "*************************:merchant-reco/merchant_ep_center.git"
"//ks/reco/merchant_nn_retrieve" = "*************************:merchant-reco/merchant-ann-recall-system.git"
"//ks/reco/photo_map" = "*************************:reco-cpp/photo_map.git"
"//ks/reco_proto" = "*************************:reco-cpp/reco_proto.git"
"//ksearch-index" = "*************************:se/ksearch-index.git"
"//kuaishou-slide-mix-rank" = "*************************:reco/kuaishou-slide-mix-rank.git"
"//merchant-reco-access-layer" = "*************************:merchant-reco/merchant-reco-access-layer.git"
"//se" = "*************************:se/kuaishou-search-component.git"
"//slide-leaf-dragon" = "*************************:reco-dev/slide-leaf-dragon.git"
"//slide-nearline-analysis" = "*************************:reco/slide-nearline-analysis.git"
"//sophon" = "*************************:se/arch/sophon.git"
"//teams/ad" = "*************************:ad/ad.git"
"//teams/reco-arch/embedding_manager" = "*************************:reco-arch/embedding_manager.git"
"//teams/reco-arch/fdk" = "*************************:reco-arch/fdk.git"
"//teams/reco-arch/kaifg" = "*************************:reco-arch/kaifg.git"
"//teams/reco-arch/rocksdb" = "*************************:reco-arch/rocksdb.git"
"//teams/reco-arch/uni-predict" = "*************************:reco-arch/uni-predict.git"
"//teams/reco-arch/uni-predict-v2" = "*************************:reco-arch/uni-predict-v2.git"
"//teams/reco-model/serve/tdm_project" = "*************************:reco-model/tdm_project.git"
"""
ad_deps = """
[git_deps]
"//algo-engine/dict-manager" = "*************************:algo-engine/dict-manager.git"
"//algo-engine/kaiworks/kaiworks-stream-proto" = "*************************:algo-engine/kaiworks/kaiworks-stream-proto.git"
"//algo-engine/shared-proto" = "*************************:algo-engine/shared-proto.git"
"//cloud-storage/libhdfsforalluxio" = "*************************:HDFS/libhdfsforalluxio.git"
"//dragon" = "*************************:reco-cpp/dragon.git"
"//ks/reco_proto" = "*************************:reco-cpp/reco_proto.git"
"//minecraft" = "*************************:video-editor/kuaishou-video-minecraft.git"
"//picasso_booster" = "*************************:ad-infra/picasso-proto.git"
"//se" = "*************************:se/kuaishou-search-component.git"
"//teams/ad" = "*************************:ad/ad.git"
"//teams/hetero" = "*************************:hetero_grp/hetero.git"
"//teams/reco-arch/embedding_manager" = "*************************:reco-arch/embedding_manager.git"
"//teams/reco-arch/fdk" = "*************************:reco-arch/fdk.git"
"//teams/reco-arch/kaifg" = "*************************:reco-arch/kaifg.git"
"//teams/reco-arch/rocksdb" = "*************************:reco-arch/rocksdb.git"
"//teams/reco-arch/uni-predict" = "*************************:reco-arch/uni-predict.git"
"//teams/reco-model/serve/tdm_project" = "*************************:reco-model/tdm_project.git"
"//xlib" = "*************************:ad-infra/xlib.git"""

# 处理路径转换
dragon_paths = set()
ad_paths = set()

def process_deps(deps_str):
    paths = set()
    for line in deps_str.split('\n'):
        if '=' in line:
            path = line.split('=')[1].strip().strip('"')
            path = path.replace(':',r'/')
            path = path.replace('git@', 'https://')
            if path.endswith('.git'):
                path = path[:-4]
            paths.add(path)
    return paths

dragon_paths = process_deps(dragon_deps)
ad_paths = process_deps(ad_deps)

print("仅dragon依赖:")
for path in sorted(dragon_paths - ad_paths):
    print(path)

print("\n仅ad依赖:")
for path in sorted(ad_paths - dragon_paths):
    print(path)

print("\ndragon和ad共同依赖:")
for path in sorted(dragon_paths & ad_paths):
    print(path)


